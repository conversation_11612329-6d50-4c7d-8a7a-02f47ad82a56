#!/bin/bash

# Quick test script to verify web packages are loading
# This will build and run the web app to test subscription packages

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing RevenueCat Web Packages${NC}"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo -e "${RED}❌ Error: Please run this script from the flutter-animalia directory${NC}"
    exit 1
fi

echo -e "${BLUE}🔨 Building web app...${NC}"
flutter build web --dart-define=FLUTTER_ENV=development

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Web build successful${NC}"
else
    echo -e "${RED}❌ Web build failed${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}📋 Expected Web Package Loading:${NC}"
echo "1. ✅ RevenueCat Web service initialized"
echo "2. ✅ Mock packages loaded (6 packages total)"
echo "3. ✅ Bronze Monthly: 149 RON"
echo "4. ✅ Bronze Annual: 1490 RON"
echo "5. ✅ Silver Monthly: 249 RON"
echo "6. ✅ Silver Annual: 2490 RON"
echo "7. ✅ Gold Monthly: 399 RON"
echo "8. ✅ Gold Annual: 3990 RON"
echo ""
echo -e "${YELLOW}🔍 What to verify in browser:${NC}"
echo "1. Open browser console (F12)"
echo "2. Look for: '✅ Loaded 6 web subscription packages'"
echo "3. Navigate to subscription screen"
echo "4. Verify all 6 packages are displayed"
echo "5. Click 'Choose This Plan' to test purchase flow"
echo ""

echo -e "${BLUE}🌐 Starting web app on port 3002...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop${NC}"
echo ""

# Start the web app on a different port to avoid conflicts
flutter run -d chrome --dart-define=FLUTTER_ENV=development --web-port=3002
