import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:animaliaproject/providers/calendar_provider.dart';

void main() {
  group('CalendarProvider Responsive Tests', () {
    late CalendarProvider provider;

    setUp(() {
      provider = CalendarProvider();
    });

    testWidgets('getResponsiveTimeSlotHeight returns larger height for desktop', (WidgetTester tester) async {
      // Create a widget with desktop screen size
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Test desktop size (1200px width)
              final desktopHeight = provider.getResponsiveTimeSlotHeight(context);
              
              // The desktop height should be larger than mobile
              expect(desktopHeight, greaterThan(80.0));
              
              return Container();
            },
          ),
        ),
      );

      // Set desktop screen size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
    });

    testWidgets('getResponsiveTimeSlotHeight returns standard height for mobile', (WidgetTester tester) async {
      // Create a widget with mobile screen size
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Test mobile size (400px width)
              final mobileHeight = provider.getResponsiveTimeSlotHeight(context);
              
              // The mobile height should be the standard 80px
              expect(mobileHeight, equals(80.0));
              
              return Container();
            },
          ),
        ),
      );

      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpAndSettle();
    });

    testWidgets('getResponsiveStaffColumnWidth returns larger width for desktop', (WidgetTester tester) async {
      // Create a widget with desktop screen size
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Test desktop size (1200px width)
              final desktopWidth = provider.getResponsiveStaffColumnWidth(context);
              
              // The desktop width should be larger than mobile (140 vs 100)
              expect(desktopWidth, equals(140.0));
              
              return Container();
            },
          ),
        ),
      );

      // Set desktop screen size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
    });

    testWidgets('getResponsiveStaffColumnWidth returns standard width for mobile', (WidgetTester tester) async {
      // Create a widget with mobile screen size
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              // Test mobile size (400px width)
              final mobileWidth = provider.getResponsiveStaffColumnWidth(context);
              
              // The mobile width should be the standard 100px
              expect(mobileWidth, equals(100.0));
              
              return Container();
            },
          ),
        ),
      );

      // Set mobile screen size
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpAndSettle();
    });

    testWidgets('responsive sizing respects zoom preferences', (WidgetTester tester) async {
      // Set a custom zoom level
      provider.setTimeSlotHeight(90.0); // 90px instead of default 80px
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final responsiveHeight = provider.getResponsiveTimeSlotHeight(context);
              
              // Should scale proportionally: (100/80) * 90 = 112.5
              expect(responsiveHeight, closeTo(112.5, 0.1));
              
              return Container();
            },
          ),
        ),
      );

      // Set desktop screen size
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
    });
  });
}
