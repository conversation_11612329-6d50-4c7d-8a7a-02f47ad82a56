import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:tova_animalia_programari/models/appointment.dart';
import 'package:tova_animalia_programari/providers/calendar_provider.dart';
import 'package:tova_animalia_programari/widgets/calendar_views/day_view.dart';
import 'package:tova_animalia_programari/services/staff_service.dart';

import 'day_view_test.mocks.dart';

@GenerateMocks([CalendarProvider, StaffService])
void main() {
  group('DayView Tests', () {
    late MockCalendarProvider mockCalendarProvider;
    late List<Appointment> testAppointments;
    late DateTime testDate;
    late Map<String, dynamic> testBusinessHours;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testDate = DateTime(2024, 1, 15, 10, 0); // Monday, 10 AM
      testAppointments = [];
      testBusinessHours = {
        'openTime': 9,
        'closeTime': 17,
        'lunchBreak': {
          'start': 12,
          'end': 13,
        },
      };

      // Setup mock provider defaults
      when(mockCalendarProvider.showFullDay).thenReturn(false);
      when(mockCalendarProvider.availableStaff).thenReturn([]);
      when(mockCalendarProvider.selectedStaff).thenReturn([]);
      when(mockCalendarProvider.getResponsiveTimeSlotHeight(any)).thenReturn(80.0);
      when(mockCalendarProvider.getResponsiveStaffColumnWidth(any)).thenReturn(120.0);
      when(mockCalendarProvider.isWorkingDay(any)).thenReturn(true);
      when(mockCalendarProvider.getBlockedTimesForDate(any)).thenReturn([]);
      when(mockCalendarProvider.getTimeSlotStyling(any, any)).thenReturn(
        TimeSlotStyling(
          isAvailable: true,
          isGreyedOut: false,
          isInteractive: true,
          disabledReason: null,
        ),
      );
    });

    testWidgets('DayView renders without PageView', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DayView(
              selectedDate: testDate,
              appointments: testAppointments,
              onAppointmentTap: (appointment) {},
              businessHours: testBusinessHours,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that PageView is not present
      expect(find.byType(PageView), findsNothing);
      
      // Verify that horizontal scrolling is present
      expect(find.byType(SingleChildScrollView), findsWidgets);
    });

    testWidgets('DayView shows all selected staff without 3-groomer limit', (WidgetTester tester) async {
      // Create 5 test staff members
      final testStaff = List.generate(5, (index) => StaffResponse(
        id: 'staff_$index',
        name: 'Staff $index',
        displayName: 'Staff $index',
        groomerRole: GroomerRole.groomer,
      ));

      when(mockCalendarProvider.availableStaff).thenReturn(testStaff);
      when(mockCalendarProvider.selectedStaff).thenReturn(testStaff.map((s) => s.id).toList());

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DayView(
              selectedDate: testDate,
              appointments: testAppointments,
              onAppointmentTap: (appointment) {},
              businessHours: testBusinessHours,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that all 5 staff members are rendered (not limited to 3)
      for (int i = 0; i < 5; i++) {
        expect(find.text('Staff $i'), findsOneWidget);
      }

      // Verify no overflow warning is shown
      expect(find.textContaining('Afișare limitată la 3'), findsNothing);
    });

    testWidgets('DayView supports horizontal scrolling for staff columns', (WidgetTester tester) async {
      // Create 6 test staff members to ensure horizontal scrolling is needed
      final testStaff = List.generate(6, (index) => StaffResponse(
        id: 'staff_$index',
        name: 'Staff $index',
        displayName: 'Staff $index',
        groomerRole: GroomerRole.groomer,
      ));

      when(mockCalendarProvider.availableStaff).thenReturn(testStaff);
      when(mockCalendarProvider.selectedStaff).thenReturn(testStaff.map((s) => s.id).toList());

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DayView(
              selectedDate: testDate,
              appointments: testAppointments,
              onAppointmentTap: (appointment) {},
              businessHours: testBusinessHours,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find horizontal scroll views
      final horizontalScrollViews = find.byWidgetPredicate(
        (widget) => widget is SingleChildScrollView && 
                   widget.scrollDirection == Axis.horizontal,
      );
      
      expect(horizontalScrollViews, findsAtLeastNWidgets(1));
    });

    testWidgets('DayView shows current date in header', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DayView(
              selectedDate: testDate,
              appointments: testAppointments,
              onAppointmentTap: (appointment) {},
              businessHours: testBusinessHours,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the day header shows the selected date
      expect(find.textContaining('luni'), findsOneWidget); // Monday in Romanian
      expect(find.textContaining('15 ianuarie 2024'), findsOneWidget);
    });

    testWidgets('DayView maintains width calculation for visual layout', (WidgetTester tester) async {
      // Create 4 test staff members
      final testStaff = List.generate(4, (index) => StaffResponse(
        id: 'staff_$index',
        name: 'Staff $index',
        displayName: 'Staff $index',
        groomerRole: GroomerRole.groomer,
      ));

      when(mockCalendarProvider.availableStaff).thenReturn(testStaff);
      when(mockCalendarProvider.selectedStaff).thenReturn(testStaff.map((s) => s.id).toList());

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DayView(
              selectedDate: testDate,
              appointments: testAppointments,
              onAppointmentTap: (appointment) {},
              businessHours: testBusinessHours,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the widget renders without overflow errors
      expect(tester.takeException(), isNull);
      
      // Verify all staff are present
      for (int i = 0; i < 4; i++) {
        expect(find.text('Staff $i'), findsOneWidget);
      }
    });
  });
}

// Mock TimeSlotStyling class for testing
class TimeSlotStyling {
  final bool isAvailable;
  final bool isGreyedOut;
  final bool isInteractive;
  final String? disabledReason;

  TimeSlotStyling({
    required this.isAvailable,
    required this.isGreyedOut,
    required this.isInteractive,
    this.disabledReason,
  });
}
