import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:animaliaproject/widgets/calendar_views/week_view.dart';
import 'package:animaliaproject/providers/calendar_provider.dart';
import 'package:animaliaproject/models/appointment.dart';

void main() {
  group('WeekView Responsive Layout Tests', () {
    testWidgets('WeekView should use flexible layout for day columns', (WidgetTester tester) async {
      // Create a mock calendar provider
      final mockProvider = CalendarProvider();
      
      // Create test data
      final testWeek = DateTime(2024, 1, 1); // Monday
      final testAppointments = <DateTime, List<Appointment>>{};
      final testBusinessHours = {
        'openTime': 9,
        'closeTime': 17,
        'lunchBreak': {'start': 12, 'end': 13},
      };

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockProvider,
            child: Scaffold(
              body: WeekView(
                selectedWeek: testWeek,
                appointmentsByDay: testAppointments,
                onAppointmentTap: (appointment) {},
                businessHours: testBusinessHours,
              ),
            ),
          ),
        ),
      );

      // Set different screen sizes to test responsive behavior
      
      // Test mobile size (400px width)
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpAndSettle();
      
      // Verify WeekView is present
      expect(find.byType(WeekView), findsOneWidget);
      
      // Test tablet size (800px width)
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpAndSettle();
      
      // Verify WeekView still works
      expect(find.byType(WeekView), findsOneWidget);
      
      // Test desktop size (1200px width)
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      await tester.pumpAndSettle();
      
      // Verify WeekView adapts to larger screen
      expect(find.byType(WeekView), findsOneWidget);
    });

    testWidgets('WeekView should calculate dynamic column widths based on screen size', (WidgetTester tester) async {
      final mockProvider = CalendarProvider();
      final testWeek = DateTime(2024, 1, 1);
      final testAppointments = <DateTime, List<Appointment>>{};
      final testBusinessHours = {
        'openTime': 9,
        'closeTime': 17,
        'lunchBreak': {'start': 12, 'end': 13},
      };

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockProvider,
            child: Scaffold(
              body: WeekView(
                selectedWeek: testWeek,
                appointmentsByDay: testAppointments,
                onAppointmentTap: (appointment) {},
                businessHours: testBusinessHours,
              ),
            ),
          ),
        ),
      );

      // Test that the widget builds without errors on different screen sizes
      const testSizes = [
        Size(320, 568), // Small mobile
        Size(375, 667), // iPhone
        Size(768, 1024), // Tablet
        Size(1024, 768), // Tablet landscape
        Size(1440, 900), // Desktop
      ];

      for (final size in testSizes) {
        await tester.binding.setSurfaceSize(size);
        await tester.pumpAndSettle();
        
        // Verify the widget renders without errors
        expect(find.byType(WeekView), findsOneWidget);
        expect(tester.takeException(), isNull);
      }
    });
  });
}
