import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Platform-Specific Terms Logic Tests', () {
    test('should return Apple EULA URL for iOS platform', () {
      const String appleEulaUrl = 'https://www.apple.com/legal/internet-services/itunes/dev/stdeula/';
      const String customTermsUrl = 'https://animalia-programari.ro/terms-of-service.html';
      
      // Simulate iOS platform logic
      String getPlatformSpecificTermsUrl(TargetPlatform platform) {
        if (platform == TargetPlatform.iOS) {
          return appleEulaUrl;
        } else {
          return customTermsUrl;
        }
      }
      
      // Test iOS
      expect(getPlatformSpecificTermsUrl(TargetPlatform.iOS), equals(appleEulaUrl));
      
      // Test Android
      expect(getPlatformSpecificTermsUrl(TargetPlatform.android), equals(customTermsUrl));
      
      // Test Web (simulated as Linux)
      expect(getPlatformSpecificTermsUrl(TargetPlatform.linux), equals(customTermsUrl));
    });

    test('should return correct display names for different platforms', () {
      String getPlatformTermsDisplayName(TargetPlatform platform) {
        if (platform == TargetPlatform.iOS) {
          return 'Terms of Use (EULA)';
        } else {
          return 'Terms of Service';
        }
      }
      
      // Test iOS
      expect(getPlatformTermsDisplayName(TargetPlatform.iOS), equals('Terms of Use (EULA)'));
      
      // Test Android
      expect(getPlatformTermsDisplayName(TargetPlatform.android), equals('Terms of Service'));
      
      // Test Web (simulated as Linux)
      expect(getPlatformTermsDisplayName(TargetPlatform.linux), equals('Terms of Service'));
    });
  });
}
