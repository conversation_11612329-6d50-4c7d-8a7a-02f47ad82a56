import 'package:flutter_test/flutter_test.dart';

import '../../../lib/models/salon_subscription.dart';
import '../../../lib/providers/subscription_provider.dart';
import '../../../lib/services/subscription/revenue_cat_config.dart';
import '../../../lib/services/subscription/revenue_cat_subscription_service.dart';

void main() {
  group('Subscription Purchase Tests', () {
    late SubscriptionProvider subscriptionProvider;

    setUp(() {
      subscriptionProvider = SubscriptionProvider();
    });

    group('Product ID Configuration Tests', () {
      test('should have correct product ID format for web', () {
        final webProductIds = RevenueCatConfig.productIdsWeb;

        // Freelancer should have proper product ID format
        expect(webProductIds['bronze_monthly'], startsWith('prod'));
        expect(webProductIds['bronze_monthly'], equals('prod0c00a8266'));

        // Team and Enterprise should be placeholders (not package keys)
        expect(webProductIds['silver_monthly'], isNot(equals('zywzsjlgyufqlbqp')));
        expect(webProductIds['gold_monthly'], isNot(equals('gxcbrkpwtrvshoiwf')));

        // All web product IDs should follow the pattern or be placeholders
        for (final productId in webProductIds.values) {
          expect(
            productId.startsWith('prod') || productId.contains('placeholder'),
            isTrue,
            reason: 'Product ID $productId should start with "prod" or be a placeholder',
          );
        }
      });

      test('should have different product IDs for different platforms', () {
        final iosProductIds = RevenueCatConfig.productIdsIOS;
        final androidProductIds = RevenueCatConfig.productIdsAndroid;
        final webProductIds = RevenueCatConfig.productIdsWeb;

        // iOS and Android should use bundle identifier format
        expect(iosProductIds['bronze_monthly'], contains('ro.animaliaprogramari.animalia'));
        expect(androidProductIds['bronze_monthly'], contains('animalia_freelancer'));

        // Web should use RevenueCat product ID format
        expect(webProductIds['bronze_monthly'], startsWith('prod'));
      });
    });

    group('Subscription Provider Tests', () {
      test('should detect purchase cancellation correctly', () {
        final provider = SubscriptionProvider();

        expect(provider.isPurchaseCancellation('User cancelled'), isTrue);
        expect(provider.isPurchaseCancellation('Purchase was canceled'), isTrue);
        expect(provider.isPurchaseCancellation('user_cancelled'), isTrue);
        expect(provider.isPurchaseCancellation('Achiziția a fost anulată'), isTrue);
        expect(provider.isPurchaseCancellation('Network error'), isFalse);
        expect(provider.isPurchaseCancellation('Invalid product'), isFalse);
      });

      test('should initialize with correct default values', () {
        final provider = SubscriptionProvider();

        expect(provider.currentSubscription, isNull);
        expect(provider.availablePackages, isEmpty);
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
      });
    });

    group('Configuration Validation Tests', () {
      test('should have valid RevenueCat configuration structure', () {
        // Test that all required product ID maps exist
        expect(RevenueCatConfig.productIdsIOS, isNotEmpty);
        expect(RevenueCatConfig.productIdsAndroid, isNotEmpty);
        expect(RevenueCatConfig.productIdsWeb, isNotEmpty);

        // Test that all tiers are represented
        final webProductIds = RevenueCatConfig.productIdsWeb;
        expect(webProductIds.containsKey('bronze_monthly'), isTrue);
        expect(webProductIds.containsKey('silver_monthly'), isTrue);
        expect(webProductIds.containsKey('gold_monthly'), isTrue);
      });

      test('should have consistent product ID keys across platforms', () {
        final iosKeys = RevenueCatConfig.productIdsIOS.keys.toSet();
        final androidKeys = RevenueCatConfig.productIdsAndroid.keys.toSet();
        final webKeys = RevenueCatConfig.productIdsWeb.keys.toSet();

        // All platforms should have the same keys
        expect(iosKeys, equals(androidKeys));
        expect(androidKeys, equals(webKeys));
      });
    });

    group('Subscription Tier Validation Tests', () {
      test('should have all required subscription tiers', () {
        final tiers = SubscriptionTier.values;

        expect(tiers, contains(SubscriptionTier.freelancer));
        expect(tiers, contains(SubscriptionTier.team));
        expect(tiers, contains(SubscriptionTier.enterprise));
      });

      test('should map product IDs to correct tiers', () {
        // Test the mapping logic without requiring actual Package objects
        expect('freelancer_monthly'.contains('freelancer'), isTrue);
        expect('team_monthly'.contains('team'), isTrue);
        expect('enterprise_monthly'.contains('enterprise'), isTrue);

        // Test alternative naming
        expect('bronze_monthly'.contains('bronze'), isTrue);
        expect('silver_monthly'.contains('silver'), isTrue);
        expect('gold_monthly'.contains('gold'), isTrue);
      });
    });

    group('Error Handling Tests', () {
      test('should detect purchase cancellation correctly', () {
        final provider = SubscriptionProvider();

        expect(provider.isPurchaseCancellation('User cancelled'), isTrue);
        expect(provider.isPurchaseCancellation('Purchase was canceled'), isTrue);
        expect(provider.isPurchaseCancellation('user_cancelled'), isTrue);
        expect(provider.isPurchaseCancellation('Achiziția a fost anulată'), isTrue);
        expect(provider.isPurchaseCancellation('Network error'), isFalse);
        expect(provider.isPurchaseCancellation('Invalid product'), isFalse);
      });

      test('should handle platform-specific errors', () {
        // Test that web-specific errors are handled appropriately
        expect(() => RevenueCatWebService.purchaseSubscription(
          package: WebPackage(
            id: 'test',
            name: 'Test',
            tier: SubscriptionTier.freelancer,
            priceString: '$9.99',
            billingPeriod: BillingPeriod.monthly,
          ),
          salonId: 'test_salon',
        ), throwsA(isA<Exception>()));
      });
    });
  });
}

// Helper methods for testing

// Extension to make private methods testable
extension RevenueCatWebServiceTest on RevenueCatWebService {
  static List<WebPackage> createMockWebPackages() {
    return [
      WebPackage(
        id: 'prod0c00a8266',
        name: 'Freelancer Monthly',
        tier: SubscriptionTier.freelancer,
        priceString: '$9.99',
        billingPeriod: BillingPeriod.monthly,
      ),
      WebPackage(
        id: 'prod_team_monthly_placeholder',
        name: 'Team Monthly',
        tier: SubscriptionTier.team,
        priceString: '$19.99',
        billingPeriod: BillingPeriod.monthly,
      ),
      WebPackage(
        id: 'prod_enterprise_monthly_placeholder',
        name: 'Enterprise Monthly',
        tier: SubscriptionTier.enterprise,
        priceString: '$39.99',
        billingPeriod: BillingPeriod.monthly,
      ),
    ];
  }

  static String generatePurchaseLink(WebPackage package, String userId) {
    final linkToken = 'test_link_token';
    final environment = kDebugMode ? 'sandbox/' : '';
    return 'https://pay.rev.cat/$environment$linkToken/$userId';
  }
}
