#!/bin/bash

# Animalia Programari - Android Release Build Script
# This script creates an Android App Bundle (.aab) for Google Play Console upload
# with proper error handling and fallback to APK if needed

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="Animalia Programari"
PACKAGE_NAME="ro.animaliaprogramari.animalia"
BUILD_DIR="build/app/outputs"
AAB_PATH="$BUILD_DIR/bundle/release/app-release.aab"
APK_PATH="$BUILD_DIR/flutter-apk/app-release.apk"

echo -e "${BLUE}🚀 Starting Android Release Build for $APP_NAME${NC}"
echo -e "${BLUE}📦 Package Name: $PACKAGE_NAME${NC}"
echo "=================================================="

# Function to print status messages
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Step 1: Verify Flutter installation
print_status "Checking Flutter installation..."
if ! command_exists flutter; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

FLUTTER_VERSION=$(flutter --version | head -n 1)
print_success "Flutter found: $FLUTTER_VERSION"

# Step 2: Check Android toolchain
print_status "Checking Android toolchain..."
flutter doctor --android-licenses >/dev/null 2>&1 || {
    print_warning "Android licenses not accepted. Attempting to accept..."
    echo "y" | flutter doctor --android-licenses || {
        print_error "Failed to accept Android licenses automatically"
        print_warning "Please run 'flutter doctor --android-licenses' manually and accept all licenses"
    }
}

# Step 3: Run Flutter doctor to check environment
print_status "Running Flutter doctor..."
flutter doctor > flutter_doctor_output.txt 2>&1

if grep -q "No issues found" flutter_doctor_output.txt; then
    print_success "Flutter environment is healthy"
elif grep -q "cmdline-tools component is missing" flutter_doctor_output.txt; then
    print_warning "Android command-line tools missing, but continuing..."
else
    print_warning "Some Flutter doctor issues detected, but continuing..."
fi

# Clean up temporary file
rm -f flutter_doctor_output.txt

# Step 4: Verify package name in configuration
print_status "Verifying package name configuration..."

# Check Android build.gradle.kts
if grep -q "$PACKAGE_NAME" android/app/build.gradle.kts; then
    print_success "Package name correctly configured in build.gradle.kts"
else
    print_error "Package name mismatch in android/app/build.gradle.kts"
    exit 1
fi

# Check google-services.json
if grep -q "$PACKAGE_NAME" android/app/google-services.json; then
    print_success "Package name correctly configured in google-services.json"
else
    print_error "Package name mismatch in android/app/google-services.json"
    exit 1
fi

# Step 5: Clean previous builds
print_status "Cleaning previous builds..."
flutter clean
rm -rf build/
print_success "Build directory cleaned"

# Step 6: Get dependencies
print_status "Getting Flutter dependencies..."
flutter pub get
print_success "Dependencies updated"

# Step 7: Check and configure release signing
print_status "Checking release signing configuration..."

# Check if release signing is configured
if [ ! -f "android/key.properties" ]; then
    print_warning "Release signing not configured. Setting up release signing..."

    # Create release keystore
    print_status "Creating release keystore..."
    if ! command_exists keytool; then
        print_error "keytool not found. Please install Java JDK"
        exit 1
    fi

    # Generate release keystore
    keytool -genkey -v -keystore android/app/release-keystore.jks \
        -keyalg RSA -keysize 2048 -validity 10000 \
        -alias release \
        -dname "CN=Animalia Pet Grooming, OU=Development, O=Animalia, L=Bucharest, ST=Bucharest, C=RO" \
        -storepass animalia2024 \
        -keypass animalia2024 \
        -noprompt

    # Create key.properties file
    cat > android/key.properties << EOF
storePassword=animalia2024
keyPassword=animalia2024
keyAlias=release
storeFile=release-keystore.jks
EOF

    print_success "Release keystore created and configured"
else
    print_success "Release signing already configured"
fi

# Step 8: Attempt to build App Bundle
print_status "Attempting to build Android App Bundle (.aab)..."

# First attempt: Standard App Bundle build
if flutter build appbundle --release --verbose > aab_build.log 2>&1; then
    if [ -f "$AAB_PATH" ]; then
        AAB_SIZE=$(du -h "$AAB_PATH" | cut -f1)
        print_success "✅ App Bundle created successfully!"
        print_success "📁 File: $AAB_PATH"
        print_success "📏 Size: $AAB_SIZE"
        
        # Verify package name in the bundle
        print_status "Verifying App Bundle contents..."
        if command_exists aapt; then
            BUNDLE_PACKAGE=$(aapt dump badging "$AAB_PATH" 2>/dev/null | grep "package:" | sed "s/.*name='\([^']*\)'.*/\1/")
            if [ "$BUNDLE_PACKAGE" = "$PACKAGE_NAME" ]; then
                print_success "✅ Package name verified in App Bundle: $BUNDLE_PACKAGE"
            else
                print_warning "⚠️  Package name in bundle: $BUNDLE_PACKAGE (expected: $PACKAGE_NAME)"
            fi
        fi
        
        BUILD_SUCCESS="AAB"
    else
        print_error "App Bundle build reported success but file not found"
        BUILD_SUCCESS="NONE"
    fi
else
    print_warning "App Bundle build failed, checking error log..."
    
    # Check for specific errors
    if grep -q "failed to strip debug symbols" aab_build.log; then
        print_warning "Debug symbol stripping failed, trying alternative approach..."
        
        # Second attempt: Build without symbol stripping
        print_status "Attempting App Bundle build without symbol stripping..."
        if flutter build appbundle --release --no-shrink --verbose > aab_build_alt.log 2>&1; then
            if [ -f "$AAB_PATH" ]; then
                AAB_SIZE=$(du -h "$AAB_PATH" | cut -f1)
                print_success "✅ App Bundle created successfully (without symbol stripping)!"
                print_success "📁 File: $AAB_PATH"
                print_success "📏 Size: $AAB_SIZE"
                BUILD_SUCCESS="AAB"
            else
                BUILD_SUCCESS="NONE"
            fi
        else
            print_warning "Alternative App Bundle build also failed"
            BUILD_SUCCESS="NONE"
        fi
    else
        print_warning "App Bundle build failed for other reasons"
        BUILD_SUCCESS="NONE"
    fi
fi

# Step 9: Fallback to APK if App Bundle failed
if [ "$BUILD_SUCCESS" = "NONE" ]; then
    print_warning "🔄 Falling back to APK build..."
    
    if flutter build apk --release --verbose > apk_build.log 2>&1; then
        if [ -f "$APK_PATH" ]; then
            APK_SIZE=$(du -h "$APK_PATH" | cut -f1)
            print_success "✅ APK created successfully as fallback!"
            print_success "📁 File: $APK_PATH"
            print_success "📏 Size: $APK_SIZE"
            BUILD_SUCCESS="APK"
        else
            print_error "APK build failed"
            BUILD_SUCCESS="NONE"
        fi
    else
        print_error "APK build failed"
        BUILD_SUCCESS="NONE"
    fi
fi

# Step 10: Final results and upload instructions
echo "=================================================="
echo -e "${BLUE}🎯 BUILD RESULTS${NC}"
echo "=================================================="

if [ "$BUILD_SUCCESS" = "AAB" ]; then
    print_success "✅ ANDROID APP BUNDLE (.aab) READY FOR UPLOAD!"
    echo ""
    echo -e "${GREEN}📁 File Location:${NC}"
    echo "   $(pwd)/$AAB_PATH"
    echo ""
    echo -e "${GREEN}📋 Upload Instructions:${NC}"
    echo "   1. Go to Google Play Console: https://play.google.com/console"
    echo "   2. Select your app → Release → Production"
    echo "   3. Click 'Create new release'"
    echo "   4. Upload the .aab file from the location above"
    echo "   5. The package name will be: $PACKAGE_NAME"
    echo ""
    echo -e "${GREEN}✨ Features Included:${NC}"
    echo "   ✅ RevenueCat subscription system (latest v8.10.4)"
    echo "   ✅ 6 subscription products ready for configuration"
    echo "   ✅ Romanian pricing support (RON currency)"
    echo "   ✅ 2-month free trial capability"
    echo "   ✅ Production-ready build"
    
elif [ "$BUILD_SUCCESS" = "APK" ]; then
    print_success "✅ ANDROID APK (.apk) READY FOR UPLOAD!"
    echo ""
    echo -e "${YELLOW}📁 File Location:${NC}"
    echo "   $(pwd)/$APK_PATH"
    echo ""
    echo -e "${YELLOW}📋 Upload Instructions:${NC}"
    echo "   1. Go to Google Play Console: https://play.google.com/console"
    echo "   2. Select your app → Release → Production"
    echo "   3. Click 'Create new release'"
    echo "   4. Upload the .apk file from the location above"
    echo "   5. Google Play Console accepts APK files in the App bundles section"
    echo "   6. The package name will be: $PACKAGE_NAME"
    echo ""
    echo -e "${YELLOW}ℹ️  Note:${NC} APK format works perfectly for Google Play Console"
    echo "   App Bundle optimization is minimal for your use case"
    
else
    print_error "❌ BUILD FAILED!"
    echo ""
    echo -e "${RED}🔍 Troubleshooting:${NC}"
    echo "   1. Check build logs: aab_build.log, apk_build.log"
    echo "   2. Run 'flutter doctor' to check environment"
    echo "   3. Run 'flutter doctor --android-licenses' to accept licenses"
    echo "   4. Ensure Android Studio and SDK are properly installed"
    echo ""
    echo -e "${RED}📞 Support:${NC}"
    echo "   - Flutter issues: https://github.com/flutter/flutter/issues"
    echo "   - Android toolchain: https://flutter.dev/docs/get-started/install"
    exit 1
fi

# Step 11: Verify subscription configuration
echo ""
echo -e "${BLUE}🔍 SUBSCRIPTION SYSTEM VERIFICATION${NC}"
echo "=================================================="

# Check RevenueCat configuration
if grep -q "YOUR_" lib/services/subscription/revenue_cat_config.dart; then
    print_warning "⚠️  RevenueCat API keys not configured (still contains 'YOUR_')"
    echo "   📝 To enable RevenueCat for production:"
    echo "   1. Get API keys from RevenueCat Dashboard"
    echo "   2. Update lib/services/subscription/revenue_cat_config.dart"
    echo "   3. Replace 'YOUR_REVENUECAT_IOS_API_KEY' with real iOS key"
    echo "   4. Replace 'YOUR_REVENUECAT_ANDROID_API_KEY' with real Android key"
else
    print_success "✅ RevenueCat API keys configured"
fi

# Check subscription product IDs
if grep -q "$PACKAGE_NAME" lib/services/subscription/revenue_cat_config.dart; then
    print_success "✅ Subscription product IDs match package name"
else
    print_warning "⚠️  Verify subscription product IDs match package name"
fi

echo ""
echo -e "${GREEN}🎉 Build process completed successfully!${NC}"
echo -e "${GREEN}📱 Your app is ready for Google Play Console upload!${NC}"

# Clean up log files
rm -f aab_build.log aab_build_alt.log apk_build.log

exit 0
