<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "0440"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "5199791315B1F92B003AFC57"
               BuildableName = "terminal-notifier.app"
               BlueprintName = "terminal-notifier"
               ReferencedContainer = "container:Terminal Notifier.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      buildConfiguration = "Debug">
      <Testables>
      </Testables>
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "5199791315B1F92B003AFC57"
            BuildableName = "terminal-notifier.app"
            BlueprintName = "terminal-notifier"
            ReferencedContainer = "container:Terminal Notifier.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
   </TestAction>
   <LaunchAction
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      buildConfiguration = "Release"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "NO"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "5199791315B1F92B003AFC57"
            BuildableName = "terminal-notifier.app"
            BlueprintName = "terminal-notifier"
            ReferencedContainer = "container:Terminal Notifier.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
         <CommandLineArgument
            argument = "-group abcde"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-help"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-sound default"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-list ALL"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-list abcde"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-remove abcde"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-open &apos;http://google.com/search?q=http status codes&apos;"
            isEnabled = "YES">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-execute &apos;say &quot;OMG&quot;&apos;"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-execute &apos;ls -l&apos;"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-title Kicker"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-message &apos;Execute: rake spec&apos;"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-activate com.apple.Safari"
            isEnabled = "NO">
         </CommandLineArgument>
         <CommandLineArgument
            argument = "-sender com.apple.Safari"
            isEnabled = "NO">
         </CommandLineArgument>
      </CommandLineArguments>
      <AdditionalOptions>
      </AdditionalOptions>
   </LaunchAction>
   <ProfileAction
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      buildConfiguration = "Release"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "5199791315B1F92B003AFC57"
            BuildableName = "terminal-notifier.app"
            BlueprintName = "terminal-notifier"
            ReferencedContainer = "container:Terminal Notifier.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
