## Getting Involved

New contributors are always welcome, when it doubt please ask questions. We strive to be an open and welcoming community. Please be nice to one another.

### Coding

* Pick a task:
  * Offer feedback on open [pull requests](https://github.com/excon/excon/pulls).
  * Review open [issues](https://github.com/excon/excon/issues) for things to help on.
  * [Create an issue](https://github.com/excon/excon/issues/new) to start a discussion on additions or features.
* Fork the project, add your changes and tests to cover them in a topic branch.
* Commit your changes and rebase against `excon/excon` to ensure everything is up to date.
* [Submit a pull request](https://github.com/excon/excon/compare/).

### Non-Coding

* Work for [twitter](http://twitter.com)? I'd love to reclaim the unused [@excon](http://twitter.com/excon) account!
* Offer feedback on open [issues](https://github.com/excon/excon/issues).
* Write and help edit [documentation](https://github.com/excon/excon.github.com).
* Translate [documentation](https://github.com/excon/excon.github.com) in to other languages.
* Organize or volunteer at events.
* Discuss other ideas for contribution with [geemus](mailto:<EMAIL>).
