// For more information about this configuration visit
// https://docs.fastlane.tools/actions/scan/#scanfile

// In general, you can use the options available
// fastlane scan --help

// Remove the // in front of the line to enable the option

class Scanfile: ScanfileProtocol {
    //var scheme: String? { return "Example" }
    //var openReport: Bool { return true }
    //var clean: Bool { return true }
}
