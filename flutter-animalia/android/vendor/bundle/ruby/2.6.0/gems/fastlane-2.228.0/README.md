<h3 align="center">
  <a href="https://github.com/fastlane/fastlane/blob/master/fastlane/assets/fastlane_text.png">
  <img src="https://github.com/fastlane/fastlane/blob/master/fastlane/assets/fastlane_text.png?raw=true" alt="fastlane Logo" width="500">
  </a>
</h3>

[![Twitter: @FastlaneTools](https://img.shields.io/badge/<EMAIL>?style=flat)](https://twitter.com/FastlaneTools)
[![License](https://img.shields.io/badge/license-MIT-green.svg?style=flat)](https://github.com/fastlane/fastlane/blob/master/LICENSE)
[![Gem](https://img.shields.io/gem/v/fastlane.svg?style=flat)](https://rubygems.org/gems/fastlane)
[![Homebrew](https://img.shields.io/badge/dynamic/json.svg?url=https://formulae.brew.sh/api/formula/fastlane.json&query=$.versions.stable&label=homebrew)](https://formulae.brew.sh/formula/fastlane)
[![Build Status](https://img.shields.io/circleci/project/github/fastlane/fastlane/master.svg)](https://circleci.com/gh/fastlane/fastlane)
[![PRs welcome!](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](https://github.com/fastlane/fastlane/blob/master/CONTRIBUTING.md)

_fastlane_ is a tool for iOS and Android developers to automate tedious tasks like generating screenshots, dealing with provisioning profiles, and releasing your application.

<hr />
<h2 align="center">
  ✨ All fastlane docs were moved to <a href="https://docs.fastlane.tools/">docs.fastlane.tools</a> ✨
</h2>
<hr />

## Need Help?

Before submitting a new GitHub issue, please make sure to

- Check out [docs.fastlane.tools](https://docs.fastlane.tools)
- Search for [existing GitHub issues](https://github.com/fastlane/fastlane/issues)

If the above doesn't help, please [submit an issue](https://github.com/fastlane/fastlane/issues) on GitHub and provide information about your setup, in particular the output of the `fastlane env` command.

**Note**: If you want to report a regression in _fastlane_ (something that has worked before, but broke with a new release), please mark your issue title as such using `[Regression] Your title here`. This enables us to quickly detect and fix regressions.

## _fastlane_ team

<!-- This table is regenerated and resorted on each release -->
<table id='team'>
<tr>
<td id='iulian-onofrei'>
<a href='https://github.com/revolter'>
<img src='https://github.com/revolter.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/Revolt666'>Iulian Onofrei</a></h4>
</td>
<td id='daniel-jankowski'>
<a href='https://github.com/mollyIV'>
<img src='https://github.com/mollyIV.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/mollyIV'>Daniel Jankowski</a></h4>
</td>
<td id='danielle-tomlinson'>
<a href='https://github.com/endocrimes'>
<img src='https://github.com/endocrimes.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/endocrimes'>Danielle Tomlinson</a></h4>
</td>
<td id='jorge-revuelta-h'>
<a href='https://github.com/minuscorp'>
<img src='https://github.com/minuscorp.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/minuscorp'>Jorge Revuelta H</a></h4>
</td>
<td id='olivier-halligon'>
<a href='https://github.com/AliSoftware'>
<img src='https://github.com/AliSoftware.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/aligatr'>Olivier Halligon</a></h4>
</td>
</tr>
<tr>
<td id='satoshi-namai'>
<a href='https://github.com/ainame'>
<img src='https://github.com/ainame.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/ainame'>Satoshi Namai</a></h4>
</td>
<td id='luka-mirosevic'>
<a href='https://github.com/lmirosevic'>
<img src='https://github.com/lmirosevic.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/lmirosevic'>Luka Mirosevic</a></h4>
</td>
<td id='manu-wallner'>
<a href='https://github.com/milch'>
<img src='https://github.com/milch.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/acrooow'>Manu Wallner</a></h4>
</td>
<td id='kohki-miki'>
<a href='https://github.com/giginet'>
<img src='https://github.com/giginet.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/giginet'>Kohki Miki</a></h4>
</td>
<td id='fumiya-nakamura'>
<a href='https://github.com/nafu'>
<img src='https://github.com/nafu.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/nafu003'>Fumiya Nakamura</a></h4>
</td>
</tr>
<tr>
<td id='jérôme-lacoste'>
<a href='https://github.com/lacostej'>
<img src='https://github.com/lacostej.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/lacostej'>Jérôme Lacoste</a></h4>
</td>
<td id='max-ott'>
<a href='https://github.com/max-ott'>
<img src='https://github.com/max-ott.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/ott_max'>Max Ott</a></h4>
</td>
<td id='jimmy-dee'>
<a href='https://github.com/jdee'>
<img src='https://github.com/jdee.png' width='140px;'>
</a>
<h4 align='center'>Jimmy Dee</h4>
</td>
<td id='matthew-ellis'>
<a href='https://github.com/matthewellis'>
<img src='https://github.com/matthewellis.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/mellis1995'>Matthew Ellis</a></h4>
</td>
<td id='helmut-januschka'>
<a href='https://github.com/hjanuschka'>
<img src='https://github.com/hjanuschka.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/hjanuschka'>Helmut Januschka</a></h4>
</td>
</tr>
<tr>
<td id='stefan-natchev'>
<a href='https://github.com/snatchev'>
<img src='https://github.com/snatchev.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/snatchev'>Stefan Natchev</a></h4>
</td>
<td id='łukasz-grabowski'>
<a href='https://github.com/lucgrabowski'>
<img src='https://github.com/lucgrabowski.png' width='140px;'>
</a>
<h4 align='center'>Łukasz Grabowski</h4>
</td>
<td id='joshua-liebowitz'>
<a href='https://github.com/taquitos'>
<img src='https://github.com/taquitos.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/taquitos'>Joshua Liebowitz</a></h4>
</td>
<td id='manish-rathi'>
<a href='https://github.com/crazymanish'>
<img src='https://github.com/crazymanish.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/iammanishrathi'>Manish Rathi</a></h4>
</td>
<td id='aaron-brager'>
<a href='https://github.com/getaaron'>
<img src='https://github.com/getaaron.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/getaaron'>Aaron Brager</a></h4>
</td>
</tr>
<tr>
<td id='felix-krause'>
<a href='https://github.com/KrauseFx'>
<img src='https://github.com/KrauseFx.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/KrauseFx'>Felix Krause</a></h4>
</td>
<td id='maksym-grebenets'>
<a href='https://github.com/mgrebenets'>
<img src='https://github.com/mgrebenets.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/mgrebenets'>Maksym Grebenets</a></h4>
</td>
<td id='jan-piotrowski'>
<a href='https://github.com/janpio'>
<img src='https://github.com/janpio.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/Sujan'>Jan Piotrowski</a></h4>
</td>
<td id='josh-holtz'>
<a href='https://github.com/joshdholtz'>
<img src='https://github.com/joshdholtz.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/joshdholtz'>Josh Holtz</a></h4>
</td>
<td id='roger-oba'>
<a href='https://github.com/rogerluan'>
<img src='https://github.com/rogerluan.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/rogerluan_'>Roger Oba</a></h4>
</td>
</tr>
<tr>
<td id='andrew-mcburney'>
<a href='https://github.com/armcburney'>
<img src='https://github.com/armcburney.png' width='140px;'>
</a>
<h4 align='center'><a href='https://twitter.com/armcburney'>Andrew McBurney</a></h4>
</td>
</table>

Special thanks to all [contributors](https://github.com/fastlane/fastlane/graphs/contributors) for extending and improving _fastlane_.

## Contribute to _fastlane_

Check out [CONTRIBUTING.md](CONTRIBUTING.md) for more information on how to help with _fastlane_.

## Code of Conduct

Help us keep _fastlane_ open and inclusive. Please read and follow our [Code of Conduct](https://github.com/fastlane/fastlane/blob/master/CODE_OF_CONDUCT.md).

## Metrics

_fastlane_ tracks a few key metrics to understand how developers are using the tool and to help us know what areas need improvement. No personal/sensitive information is ever collected. Metrics that are collected include:

* The number of _fastlane_ runs
* A salted hash of the app identifier or package name, which helps us anonymously identify unique usage of _fastlane_

You can easily opt-out of metrics collection by adding `opt_out_usage` at the top of your `Fastfile` or by setting the environment variable `FASTLANE_OPT_OUT_USAGE`. [Check out the metrics code on GitHub](https://github.com/fastlane/fastlane/tree/master/fastlane_core/lib/fastlane_core/analytics)

## License

This project is licensed under the terms of the MIT license. See the [LICENSE](LICENSE) file.

> This project and all fastlane tools are in no way affiliated with Apple Inc. This project is open source under the MIT license, which means you have full access to the source code and can modify it to fit your own needs. All fastlane tools run on your own computer or server, so your credentials or other sensitive information will never leave your own computer. You are responsible for how you use fastlane tools.

<hr />
<h2 align="center">
  ✨ All fastlane docs were moved to <a href="https://docs.fastlane.tools/">docs.fastlane.tools</a> ✨
</h2>
<hr />
