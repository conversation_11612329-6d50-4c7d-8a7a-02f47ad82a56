require 'fastlane_core/configuration/config_item'
require 'credentials_manager/appfile_config'
require_relative 'module'

module Screengrab
  class Options
    DEVICE_TYPES = ["phone", "sevenInch", "tenInch", "tv", "wear"].freeze

    # Temporarily make non-Mac environments default to skipping the open summary
    # step until we make it cross-platform
    DEFAULT_SKIP_OPEN_SUMMARY = !FastlaneCore::Helper.mac?

    def self.available_options
      @options ||= [
        FastlaneCore::ConfigItem.new(key: :android_home,
                                     short_option: "-n",
                                     optional: true,
                                     code_gen_sensitive: true,
                                     default_value: ENV['ANDROID_HOME'] || ENV['ANDROID_SDK_ROOT'] || ENV['ANDROID_SDK'],
                                     default_value_dynamic: true,
                                     description: "Path to the root of your Android SDK installation, e.g. ~/tools/android-sdk-macosx"),
        FastlaneCore::ConfigItem.new(key: :build_tools_version,
                                     short_option: "-i",
                                     optional: true,
                                     description: "The Android build tools version to use, e.g. '23.0.2'",
                                     deprecated: true),
        FastlaneCore::ConfigItem.new(key: :locales,
                                     description: "A list of locales which should be used",
                                     short_option: "-q",
                                     type: Array,
                                     default_value: ['en-US']),
        FastlaneCore::ConfigItem.new(key: :clear_previous_screenshots,
                                     env_name: 'SCREENGRAB_CLEAR_PREVIOUS_SCREENSHOTS',
                                     description: "Enabling this option will automatically clear previously generated screenshots before running screengrab",
                                     default_value: false,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :output_directory,
                                     short_option: "-o",
                                     env_name: "SCREENGRAB_OUTPUT_DIRECTORY",
                                     description: "The directory where to store the screenshots",
                                     default_value: File.join("fastlane", "metadata", "android")),
        FastlaneCore::ConfigItem.new(key: :skip_open_summary,
                                     env_name: 'SCREENGRAB_SKIP_OPEN_SUMMARY',
                                     description: "Don't open the summary after running _screengrab_",
                                     default_value: DEFAULT_SKIP_OPEN_SUMMARY,
                                     default_value_dynamic: true,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :app_package_name,
                                     env_name: 'SCREENGRAB_APP_PACKAGE_NAME',
                                     short_option: "-a",
                                     description: "The package name of the app under test (e.g. com.yourcompany.yourapp)",
                                     code_gen_sensitive: true,
                                     default_value: CredentialsManager::AppfileConfig.try_fetch_value(:package_name),
                                     default_value_dynamic: true),
        FastlaneCore::ConfigItem.new(key: :tests_package_name,
                                     env_name: 'SCREENGRAB_TESTS_PACKAGE_NAME',
                                     optional: true,
                                     description: "The package name of the tests bundle (e.g. com.yourcompany.yourapp.test)"),
        FastlaneCore::ConfigItem.new(key: :use_tests_in_packages,
                                     env_name: 'SCREENGRAB_USE_TESTS_IN_PACKAGES',
                                     optional: true,
                                     short_option: "-p",
                                     type: Array,
                                     description: "Only run tests in these Java packages"),
        FastlaneCore::ConfigItem.new(key: :use_tests_in_classes,
                                     env_name: 'SCREENGRAB_USE_TESTS_IN_CLASSES',
                                     optional: true,
                                     short_option: "-l",
                                     type: Array,
                                     description: "Only run tests in these Java classes"),
        FastlaneCore::ConfigItem.new(key: :launch_arguments,
                                     env_name: 'SCREENGRAB_LAUNCH_ARGUMENTS',
                                     optional: true,
                                     short_option: "-e",
                                     type: Array,
                                     description: "Additional launch arguments"),
        FastlaneCore::ConfigItem.new(key: :test_instrumentation_runner,
                                     env_name: 'SCREENGRAB_TEST_INSTRUMENTATION_RUNNER',
                                     optional: true,
                                     default_value: 'androidx.test.runner.AndroidJUnitRunner',
                                     description: "The fully qualified class name of your test instrumentation runner"),
        FastlaneCore::ConfigItem.new(key: :ending_locale,
                                     env_name: 'SCREENGRAB_ENDING_LOCALE',
                                     optional: true,
                                     default_value: 'en-US',
                                     description: "Return the device to this locale after running tests",
                                     deprecated: true),
        FastlaneCore::ConfigItem.new(key: :use_adb_root,
                                     env_name: 'SCREENGRAB_USE_ADB_ROOT',
                                     description: "Restarts the adb daemon using `adb root` to allow access to screenshots directories on device. Use if getting 'Permission denied' errors",
                                     deprecated: true,
                                     default_value: false,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :app_apk_path,
                                     env_name: 'SCREENGRAB_APP_APK_PATH',
                                     optional: true,
                                     description: "The path to the APK for the app under test",
                                     short_option: "-k",
                                     code_gen_sensitive: true,
                                     default_value: Dir[File.join("app", "build", "outputs", "apk", "app-debug.apk")].last,
                                     default_value_dynamic: true,
                                     verify_block: proc do |value|
                                       UI.user_error!("Could not find APK file at path '#{value}'") unless File.exist?(value)
                                     end),
        FastlaneCore::ConfigItem.new(key: :tests_apk_path,
                                     env_name: 'SCREENGRAB_TESTS_APK_PATH',
                                     optional: true,
                                     description: "The path to the APK for the tests bundle",
                                     short_option: "-b",
                                     code_gen_sensitive: true,
                                     default_value: Dir[File.join("app", "build", "outputs", "apk", "app-debug-androidTest-unaligned.apk")].last,
                                     default_value_dynamic: true,
                                     verify_block: proc do |value|
                                       UI.user_error!("Could not find APK file at path '#{value}'") unless File.exist?(value)
                                     end),
        FastlaneCore::ConfigItem.new(key: :specific_device,
                                     env_name: 'SCREENGRAB_SPECIFIC_DEVICE',
                                     optional: true,
                                     description: "Use the device or emulator with the given serial number or qualifier",
                                     short_option: "-s"),
        FastlaneCore::ConfigItem.new(key: :device_type,
                                     env_name: 'SCREENGRAB_DEVICE_TYPE',
                                     description: "Type of device used for screenshots. Matches Google Play Types (phone, sevenInch, tenInch, tv, wear)",
                                     short_option: "-d",
                                     default_value: "phone",
                                     verify_block: proc do |value|
                                       UI.user_error!("device_type must be one of: #{DEVICE_TYPES}") unless DEVICE_TYPES.include?(value)
                                     end),
        FastlaneCore::ConfigItem.new(key: :exit_on_test_failure,
                                     env_name: 'EXIT_ON_TEST_FAILURE',
                                     description: "Whether or not to exit Screengrab on test failure. Exiting on failure will not copy screenshots to local machine nor open screenshots summary",
                                     default_value: true,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :reinstall_app,
                                     env_name: 'SCREENGRAB_REINSTALL_APP',
                                     description: "Enabling this option will automatically uninstall the application before running it",
                                     default_value: false,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :use_timestamp_suffix,
                                     env_name: 'SCREENGRAB_USE_TIMESTAMP_SUFFIX',
                                     description: "Add timestamp suffix to screenshot filename",
                                     default_value: true,
                                     type: Boolean),
        FastlaneCore::ConfigItem.new(key: :adb_host,
                                     env_name: 'SCREENGRAB_ADB_HOST',
                                     description: "Configure the host used by adb to connect, allows running on remote devices farm",
                                     optional: true)
      ]
    end
  end
end
