lib = File.expand_path("lib", __dir__)
$LOAD_PATH.unshift(lib) unless $LOAD_PATH.include?(lib)
require '<%= require_path %>/version'

Gem::Specification.new do |spec|
  spec.name          = '<%= gem_name %>'
  spec.version       = Fastlane::<%= plugin_name.fastlane_class %>::VERSION
  spec.author        = '<%= author %>'
  spec.email         = '<%= email %>'

  spec.summary       = '<%= summary %>'
  # spec.homepage      = "https://github.com/<GITHUB_USERNAME>/<%= gem_name %>"
  spec.license       = "MIT"

  spec.files         = Dir["lib/**/*"] + %w(README.md LICENSE)
  spec.require_paths = ['lib']
  spec.metadata['rubygems_mfa_required'] = 'true'
  spec.required_ruby_version = '>= 2.6'

  # Don't add a dependency to fastlane or fastlane_re
  # since this would cause a circular dependency

  # spec.add_dependency 'your-dependency', '~> 1.0.0'
end
