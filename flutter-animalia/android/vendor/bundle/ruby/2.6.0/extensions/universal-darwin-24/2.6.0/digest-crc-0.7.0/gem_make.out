current directory: /Users/<USER>/Documents/Tova-Animalia-Programari/flutter-animalia/android/vendor/bundle/ruby/2.6.0/gems/digest-crc-0.7.0/ext/digest
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -rrubygems /Users/<USER>/Documents/Tova-Animalia-Programari/flutter-animalia/android/vendor/bundle/ruby/2.6.0/gems/rake-13.3.0/exe/rake RUBYARCHDIR\=/Users/<USER>/Documents/Tova-Animalia-Programari/flutter-animalia/android/vendor/bundle/ruby/2.6.0/extensions/universal-darwin-24/2.6.0/digest-crc-0.7.0 RUBYLIBDIR\=/Users/<USER>/Documents/Tova-Animalia-Programari/flutter-animalia/android/vendor/bundle/ruby/2.6.0/extensions/universal-darwin-24/2.6.0/digest-crc-0.7.0
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_genibus.c
compiling crc16_genibus_ext.c
crc16_genibus_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_genibus_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_genibus_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_genibus_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_genibus_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_genibus_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_genibus_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_genibus_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_genibus_ext.c:25:46: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Genibus = rb_const_get(mDigest, rb_intern("CRC16Genibus"));
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_genibus_ext.c:25:46: note: '{' token is here
   25 |         VALUE cCRC16Genibus = rb_const_get(mDigest, rb_intern("CRC16Genibus"));
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_genibus_ext.c:25:46: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Genibus = rb_const_get(mDigest, rb_intern("CRC16Genibus"));
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_genibus_ext.c:25:46: note: ')' token is here
   25 |         VALUE cCRC16Genibus = rb_const_get(mDigest, rb_intern("CRC16Genibus"));
      |                                                     ^~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_genibus_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16.c
compiling crc16_ext.c
crc16_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_ext.c:25:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16 = rb_const_get(mDigest, rb_intern("CRC16"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ext.c:25:39: note: '{' token is here
   25 |         VALUE cCRC16 = rb_const_get(mDigest, rb_intern("CRC16"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ext.c:25:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16 = rb_const_get(mDigest, rb_intern("CRC16"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ext.c:25:39: note: ')' token is here
   25 |         VALUE cCRC16 = rb_const_get(mDigest, rb_intern("CRC16"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32c.c
compiling crc32c_ext.c
crc32c_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32c_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32c_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32c_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32c_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32c_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32c_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32c_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32c_ext.c:24:40: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32c = rb_const_get(mDigest, rb_intern("CRC32c"));
      |                                               ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32c_ext.c:24:40: note: '{' token is here
   24 |         VALUE cCRC32c = rb_const_get(mDigest, rb_intern("CRC32c"));
      |                                               ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32c_ext.c:24:40: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32c = rb_const_get(mDigest, rb_intern("CRC32c"));
      |                                               ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32c_ext.c:24:40: note: ')' token is here
   24 |         VALUE cCRC32c = rb_const_get(mDigest, rb_intern("CRC32c"));
      |                                               ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32c_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_jones.c
compiling crc64_jones_ext.c
crc64_jones_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_jones_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_jones_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_jones_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_jones_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_jones_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_jones_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_jones_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_jones_ext.c:24:44: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64Jones = rb_const_get(mDigest, rb_intern("CRC64Jones"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_jones_ext.c:24:44: note: '{' token is here
   24 |         VALUE cCRC64Jones = rb_const_get(mDigest, rb_intern("CRC64Jones"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_jones_ext.c:24:44: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64Jones = rb_const_get(mDigest, rb_intern("CRC64Jones"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_jones_ext.c:24:44: note: ')' token is here
   24 |         VALUE cCRC64Jones = rb_const_get(mDigest, rb_intern("CRC64Jones"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc64_jones_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_modbus.c
compiling crc16_modbus_ext.c
crc16_modbus_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_modbus_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_modbus_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_modbus_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_modbus_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_modbus_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_modbus_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_modbus_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_modbus_ext.c:25:45: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Modbus = rb_const_get(mDigest, rb_intern("CRC16Modbus"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_modbus_ext.c:25:45: note: '{' token is here
   25 |         VALUE cCRC16Modbus = rb_const_get(mDigest, rb_intern("CRC16Modbus"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_modbus_ext.c:25:45: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Modbus = rb_const_get(mDigest, rb_intern("CRC16Modbus"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_modbus_ext.c:25:45: note: ')' token is here
   25 |         VALUE cCRC16Modbus = rb_const_get(mDigest, rb_intern("CRC16Modbus"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_modbus_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_bzip2.c
compiling crc32_bzip2_ext.c
crc32_bzip2_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_bzip2_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_bzip2_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_bzip2_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_bzip2_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_bzip2_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_bzip2_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_bzip2_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_bzip2_ext.c:24:44: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32Bzip2 = rb_const_get(mDigest, rb_intern("CRC32BZip2"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_bzip2_ext.c:24:44: note: '{' token is here
   24 |         VALUE cCRC32Bzip2 = rb_const_get(mDigest, rb_intern("CRC32BZip2"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_bzip2_ext.c:24:44: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32Bzip2 = rb_const_get(mDigest, rb_intern("CRC32BZip2"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_bzip2_ext.c:24:44: note: ')' token is here
   24 |         VALUE cCRC32Bzip2 = rb_const_get(mDigest, rb_intern("CRC32BZip2"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_bzip2_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc12_3gpp.c
compiling crc12_3gpp_ext.c
crc12_3gpp_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc12_3gpp_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc12_3gpp_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc12_3gpp_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc12_3gpp_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc12_3gpp_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc12_3gpp_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc12_3gpp_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc12_3gpp_ext.c:24:44: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC12_3GPP = rb_const_get(mDigest, rb_intern("CRC12_3GPP"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc12_3gpp_ext.c:24:44: note: '{' token is here
   24 |         VALUE cCRC12_3GPP = rb_const_get(mDigest, rb_intern("CRC12_3GPP"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc12_3gpp_ext.c:24:44: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC12_3GPP = rb_const_get(mDigest, rb_intern("CRC12_3GPP"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc12_3gpp_ext.c:24:44: note: ')' token is here
   24 |         VALUE cCRC12_3GPP = rb_const_get(mDigest, rb_intern("CRC12_3GPP"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc12_3gpp_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_zmodem.c
compiling crc16_zmodem_ext.c
crc16_zmodem_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_zmodem_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_zmodem_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_zmodem_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_zmodem_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_zmodem_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_zmodem_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_zmodem_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_zmodem_ext.c:25:45: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16ZModem = rb_const_get(mDigest, rb_intern("CRC16ZModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_zmodem_ext.c:25:45: note: '{' token is here
   25 |         VALUE cCRC16ZModem = rb_const_get(mDigest, rb_intern("CRC16ZModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_zmodem_ext.c:25:45: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16ZModem = rb_const_get(mDigest, rb_intern("CRC16ZModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_zmodem_ext.c:25:45: note: ')' token is here
   25 |         VALUE cCRC16ZModem = rb_const_get(mDigest, rb_intern("CRC16ZModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_zmodem_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_xfer.c
compiling crc32_xfer_ext.c
crc32_xfer_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_xfer_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_xfer_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_xfer_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_xfer_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_xfer_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_xfer_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_xfer_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_xfer_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32XFER = rb_const_get(mDigest, rb_intern("CRC32XFER"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_xfer_ext.c:24:43: note: '{' token is here
   24 |         VALUE cCRC32XFER = rb_const_get(mDigest, rb_intern("CRC32XFER"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_xfer_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32XFER = rb_const_get(mDigest, rb_intern("CRC32XFER"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_xfer_ext.c:24:43: note: ')' token is here
   24 |         VALUE cCRC32XFER = rb_const_get(mDigest, rb_intern("CRC32XFER"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_xfer_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_x_25.c
compiling crc16_x_25_ext.c
crc16_x_25_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_x_25_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_x_25_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_x_25_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_x_25_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_x_25_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_x_25_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_x_25_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_x_25_ext.c:25:42: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16X25 = rb_const_get(mDigest, rb_intern("CRC16X25"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_x_25_ext.c:25:42: note: '{' token is here
   25 |         VALUE cCRC16X25 = rb_const_get(mDigest, rb_intern("CRC16X25"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_x_25_ext.c:25:42: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16X25 = rb_const_get(mDigest, rb_intern("CRC16X25"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_x_25_ext.c:25:42: note: ')' token is here
   25 |         VALUE cCRC16X25 = rb_const_get(mDigest, rb_intern("CRC16X25"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_x_25_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_kermit.c
compiling crc16_kermit_ext.c
crc16_kermit_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_kermit_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_kermit_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_kermit_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_kermit_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_kermit_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_kermit_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_kermit_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_kermit_ext.c:25:45: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Kermit = rb_const_get(mDigest, rb_intern("CRC16Kermit"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_kermit_ext.c:25:45: note: '{' token is here
   25 |         VALUE cCRC16Kermit = rb_const_get(mDigest, rb_intern("CRC16Kermit"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_kermit_ext.c:25:45: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16Kermit = rb_const_get(mDigest, rb_intern("CRC16Kermit"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_kermit_ext.c:25:45: note: ')' token is here
   25 |         VALUE cCRC16Kermit = rb_const_get(mDigest, rb_intern("CRC16Kermit"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_kermit_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc8.c
compiling crc8_ext.c
crc8_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc8_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc8_ext.c:24:38: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC8 = rb_const_get(mDigest, rb_intern("CRC8"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_ext.c:24:38: note: '{' token is here
   24 |         VALUE cCRC8 = rb_const_get(mDigest, rb_intern("CRC8"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_ext.c:24:38: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC8 = rb_const_get(mDigest, rb_intern("CRC8"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_ext.c:24:38: note: ')' token is here
   24 |         VALUE cCRC8 = rb_const_get(mDigest, rb_intern("CRC8"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc8_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_xz.c
compiling crc64_xz_ext.c
crc64_xz_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_xz_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_xz_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_xz_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_xz_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_xz_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_xz_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_xz_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_xz_ext.c:24:41: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64XZ = rb_const_get(mDigest, rb_intern("CRC64XZ"));
      |                                                ^~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_xz_ext.c:24:41: note: '{' token is here
   24 |         VALUE cCRC64XZ = rb_const_get(mDigest, rb_intern("CRC64XZ"));
      |                                                ^~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_xz_ext.c:24:41: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64XZ = rb_const_get(mDigest, rb_intern("CRC64XZ"));
      |                                                ^~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_xz_ext.c:24:41: note: ')' token is here
   24 |         VALUE cCRC64XZ = rb_const_get(mDigest, rb_intern("CRC64XZ"));
      |                                                ^~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc64_xz_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_usb.c
compiling crc16_usb_ext.c
crc16_usb_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_usb_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_usb_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_usb_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_usb_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_usb_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_usb_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_usb_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_usb_ext.c:25:42: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16USB = rb_const_get(mDigest, rb_intern("CRC16USB"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_usb_ext.c:25:42: note: '{' token is here
   25 |         VALUE cCRC16USB = rb_const_get(mDigest, rb_intern("CRC16USB"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_usb_ext.c:25:42: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16USB = rb_const_get(mDigest, rb_intern("CRC16USB"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_usb_ext.c:25:42: note: ')' token is here
   25 |         VALUE cCRC16USB = rb_const_get(mDigest, rb_intern("CRC16USB"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_usb_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32.c
compiling crc32_ext.c
crc32_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_ext.c:24:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32 = rb_const_get(mDigest, rb_intern("CRC32"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_ext.c:24:39: note: '{' token is here
   24 |         VALUE cCRC32 = rb_const_get(mDigest, rb_intern("CRC32"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_ext.c:24:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32 = rb_const_get(mDigest, rb_intern("CRC32"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_ext.c:24:39: note: ')' token is here
   24 |         VALUE cCRC32 = rb_const_get(mDigest, rb_intern("CRC32"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_xmodem.c
compiling crc16_xmodem_ext.c
crc16_xmodem_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_xmodem_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_xmodem_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_xmodem_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_xmodem_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_xmodem_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_xmodem_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_xmodem_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_xmodem_ext.c:25:45: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16XModem = rb_const_get(mDigest, rb_intern("CRC16XModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_xmodem_ext.c:25:45: note: '{' token is here
   25 |         VALUE cCRC16XModem = rb_const_get(mDigest, rb_intern("CRC16XModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_xmodem_ext.c:25:45: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16XModem = rb_const_get(mDigest, rb_intern("CRC16XModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_xmodem_ext.c:25:45: note: ')' token is here
   25 |         VALUE cCRC16XModem = rb_const_get(mDigest, rb_intern("CRC16XModem"));
      |                                                    ^~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_xmodem_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc15.c
compiling crc15_ext.c
crc15_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc15_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc15_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc15_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc15_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc15_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc15_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc15_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc15_ext.c:25:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC15 = rb_const_get(mDigest, rb_intern("CRC15"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc15_ext.c:25:39: note: '{' token is here
   25 |         VALUE cCRC15 = rb_const_get(mDigest, rb_intern("CRC15"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc15_ext.c:25:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC15 = rb_const_get(mDigest, rb_intern("CRC15"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc15_ext.c:25:39: note: ')' token is here
   25 |         VALUE cCRC15 = rb_const_get(mDigest, rb_intern("CRC15"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc15_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc24.c
compiling crc24_ext.c
crc24_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc24_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc24_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc24_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc24_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc24_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc24_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc24_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc24_ext.c:24:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC24 = rb_const_get(mDigest, rb_intern("CRC24"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc24_ext.c:24:39: note: '{' token is here
   24 |         VALUE cCRC24 = rb_const_get(mDigest, rb_intern("CRC24"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc24_ext.c:24:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC24 = rb_const_get(mDigest, rb_intern("CRC24"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc24_ext.c:24:39: note: ')' token is here
   24 |         VALUE cCRC24 = rb_const_get(mDigest, rb_intern("CRC24"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc24_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_dnp.c
compiling crc16_dnp_ext.c
crc16_dnp_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_dnp_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_dnp_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_dnp_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_dnp_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_dnp_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_dnp_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_dnp_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_dnp_ext.c:25:42: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16DNP = rb_const_get(mDigest, rb_intern("CRC16DNP"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_dnp_ext.c:25:42: note: '{' token is here
   25 |         VALUE cCRC16DNP = rb_const_get(mDigest, rb_intern("CRC16DNP"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_dnp_ext.c:25:42: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16DNP = rb_const_get(mDigest, rb_intern("CRC16DNP"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_dnp_ext.c:25:42: note: ')' token is here
   25 |         VALUE cCRC16DNP = rb_const_get(mDigest, rb_intern("CRC16DNP"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_dnp_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_posix.c
compiling crc32_posix_ext.c
crc32_posix_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_posix_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_posix_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_posix_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_posix_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_posix_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_posix_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_posix_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_posix_ext.c:24:44: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32POSIX = rb_const_get(mDigest, rb_intern("CRC32POSIX"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_posix_ext.c:24:44: note: '{' token is here
   24 |         VALUE cCRC32POSIX = rb_const_get(mDigest, rb_intern("CRC32POSIX"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_posix_ext.c:24:44: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32POSIX = rb_const_get(mDigest, rb_intern("CRC32POSIX"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_posix_ext.c:24:44: note: ')' token is here
   24 |         VALUE cCRC32POSIX = rb_const_get(mDigest, rb_intern("CRC32POSIX"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_posix_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_jam.c
compiling crc32_jam_ext.c
crc32_jam_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_jam_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_jam_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_jam_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_jam_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_jam_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_jam_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_jam_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_jam_ext.c:24:42: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32Jam = rb_const_get(mDigest, rb_intern("CRC32Jam"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_jam_ext.c:24:42: note: '{' token is here
   24 |         VALUE cCRC32Jam = rb_const_get(mDigest, rb_intern("CRC32Jam"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_jam_ext.c:24:42: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32Jam = rb_const_get(mDigest, rb_intern("CRC32Jam"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_jam_ext.c:24:42: note: ')' token is here
   24 |         VALUE cCRC32Jam = rb_const_get(mDigest, rb_intern("CRC32Jam"));
      |                                                 ^~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_jam_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc32_mpeg.c
compiling crc32_mpeg_ext.c
crc32_mpeg_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_mpeg_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_mpeg_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_mpeg_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_mpeg_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_mpeg_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_mpeg_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_mpeg_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc32_mpeg_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32MPEG = rb_const_get(mDigest, rb_intern("CRC32MPEG"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc32_mpeg_ext.c:24:43: note: '{' token is here
   24 |         VALUE cCRC32MPEG = rb_const_get(mDigest, rb_intern("CRC32MPEG"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc32_mpeg_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC32MPEG = rb_const_get(mDigest, rb_intern("CRC32MPEG"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc32_mpeg_ext.c:24:43: note: ')' token is here
   24 |         VALUE cCRC32MPEG = rb_const_get(mDigest, rb_intern("CRC32MPEG"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc32_mpeg_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64_nvme.c
compiling crc64_nvme_ext.c
crc64_nvme_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_nvme_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_nvme_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_nvme_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_nvme_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_nvme_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_nvme_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_nvme_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_nvme_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64NVMe = rb_const_get(mDigest, rb_intern("CRC64NVMe"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_nvme_ext.c:24:43: note: '{' token is here
   24 |         VALUE cCRC64NVMe = rb_const_get(mDigest, rb_intern("CRC64NVMe"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_nvme_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64NVMe = rb_const_get(mDigest, rb_intern("CRC64NVMe"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_nvme_ext.c:24:43: note: ')' token is here
   24 |         VALUE cCRC64NVMe = rb_const_get(mDigest, rb_intern("CRC64NVMe"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc64_nvme_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc64.c
compiling crc64_ext.c
crc64_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc64_ext.c:24:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64 = rb_const_get(mDigest, rb_intern("CRC64"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc64_ext.c:24:39: note: '{' token is here
   24 |         VALUE cCRC64 = rb_const_get(mDigest, rb_intern("CRC64"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc64_ext.c:24:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC64 = rb_const_get(mDigest, rb_intern("CRC64"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc64_ext.c:24:39: note: ')' token is here
   24 |         VALUE cCRC64 = rb_const_get(mDigest, rb_intern("CRC64"));
      |                                              ^~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc64_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc8_1wire.c
compiling crc8_1wire_ext.c
crc8_1wire_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_1wire_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_1wire_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_1wire_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc8_1wire_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_1wire_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_1wire_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_1wire_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc8_1wire_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC81Wire = rb_const_get(mDigest, rb_intern("CRC8_1Wire"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc8_1wire_ext.c:24:43: note: '{' token is here
   24 |         VALUE cCRC81Wire = rb_const_get(mDigest, rb_intern("CRC8_1Wire"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc8_1wire_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC81Wire = rb_const_get(mDigest, rb_intern("CRC8_1Wire"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc8_1wire_ext.c:24:43: note: ')' token is here
   24 |         VALUE cCRC81Wire = rb_const_get(mDigest, rb_intern("CRC8_1Wire"));
      |                                                  ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc8_1wire_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc16_ccitt.c
compiling crc16_ccitt_ext.c
crc16_ccitt_ext.c:9:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ccitt_ext.c:9:24: note: '{' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ccitt_ext.c:9:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ccitt_ext.c:9:24: note: ')' token is here
    9 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_ccitt_ext.c:24:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ccitt_ext.c:24:43: note: '{' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ccitt_ext.c:24:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ccitt_ext.c:24:43: note: ')' token is here
   24 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc16_ccitt_ext.c:25:44: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16CCITT = rb_const_get(mDigest, rb_intern("CRC16CCITT"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc16_ccitt_ext.c:25:44: note: '{' token is here
   25 |         VALUE cCRC16CCITT = rb_const_get(mDigest, rb_intern("CRC16CCITT"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc16_ccitt_ext.c:25:44: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   25 |         VALUE cCRC16CCITT = rb_const_get(mDigest, rb_intern("CRC16CCITT"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc16_ccitt_ext.c:25:44: note: ')' token is here
   25 |         VALUE cCRC16CCITT = rb_const_get(mDigest, rb_intern("CRC16CCITT"));
      |                                                   ^~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc16_ccitt_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -S extconf.rb
checking for stdint.h... yes
checking for stddef.h... yes
creating extconf.h
creating Makefile
make clean
make
compiling crc5.c
compiling crc5_ext.c
crc5_ext.c:8:24: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc5_ext.c:8:24: note: '{' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc5_ext.c:8:24: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc5_ext.c:8:24: note: ')' token is here
    8 |         VALUE crc_ivar_name = rb_intern("@crc");
      |                               ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc5_ext.c:23:43: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc5_ext.c:23:43: note: '{' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc5_ext.c:23:43: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc5_ext.c:23:43: note: ')' token is here
   23 |         VALUE mDigest = rb_const_get(rb_cObject, rb_intern("Digest"));
      |                                                  ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
crc5_ext.c:24:38: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC5 = rb_const_get(mDigest, rb_intern("CRC5"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
crc5_ext.c:24:38: note: '{' token is here
   24 |         VALUE cCRC5 = rb_const_get(mDigest, rb_intern("CRC5"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
crc5_ext.c:24:38: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
   24 |         VALUE cCRC5 = rb_const_get(mDigest, rb_intern("CRC5"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
crc5_ext.c:24:38: note: ')' token is here
   24 |         VALUE cCRC5 = rb_const_get(mDigest, rb_intern("CRC5"));
      |                                             ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
6 warnings generated.
linking shared-object crc5_ext.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found
