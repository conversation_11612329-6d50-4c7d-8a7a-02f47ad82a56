#!/bin/bash

# Test script for RevenueCat Web Billing implementation
# This script will start the web app and provide testing instructions

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🌐 RevenueCat Web Billing Test Script${NC}"
echo "=================================================="

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo -e "${RED}❌ Error: Please run this script from the flutter-animalia directory${NC}"
    exit 1
fi

echo -e "${BLUE}📋 Pre-flight checks...${NC}"

# Check Flutter installation
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ Flutter is not installed or not in PATH${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Flutter found: $(flutter --version | head -n 1)${NC}"

# Check if Chrome is available
if ! command -v google-chrome &> /dev/null && ! command -v chromium-browser &> /dev/null && ! command -v chrome &> /dev/null; then
    echo -e "${YELLOW}⚠️  Chrome not found in PATH, but Flutter might still find it${NC}"
else
    echo -e "${GREEN}✅ Chrome browser found${NC}"
fi

# Build the web app
echo -e "${BLUE}🔨 Building web app...${NC}"
flutter build web --dart-define=FLUTTER_ENV=development

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Web build successful${NC}"
else
    echo -e "${RED}❌ Web build failed${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🚀 Starting web app...${NC}"
echo -e "${YELLOW}📝 Testing Instructions:${NC}"
echo ""
echo "1. The web app will open in your browser"
echo "2. Sign in with your test account"
echo "3. Navigate to any premium feature (e.g., Reports, Advanced Settings)"
echo "4. Click 'Upgrade' or 'Subscribe' button"
echo "5. Verify the web subscription screen loads"
echo "6. Check that packages are displayed (may be empty if RevenueCat not configured)"
echo ""
echo -e "${YELLOW}🔍 What to look for:${NC}"
echo "✅ Web subscription screen loads without errors"
echo "✅ Platform detection works (shows web-specific UI)"
echo "✅ RevenueCat Web service initializes"
echo "✅ No console errors related to RevenueCat"
echo ""
echo -e "${YELLOW}📋 RevenueCat Configuration Status:${NC}"

# Check if RevenueCat is configured
if grep -q "YOUR_" lib/services/subscription/revenue_cat_config.dart; then
    echo -e "${YELLOW}⚠️  RevenueCat API keys contain placeholder values${NC}"
    echo "   Update lib/services/subscription/revenue_cat_config.dart with real keys"
else
    echo -e "${GREEN}✅ RevenueCat API keys appear to be configured${NC}"
fi

echo ""
echo -e "${BLUE}🌐 Starting Flutter web server...${NC}"
echo -e "${YELLOW}Press Ctrl+C to stop the server${NC}"
echo ""

# Start the web app
flutter run -d chrome --dart-define=FLUTTER_ENV=development --web-port=3000
