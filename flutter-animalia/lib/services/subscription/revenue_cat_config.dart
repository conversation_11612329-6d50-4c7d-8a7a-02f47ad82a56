import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../config/environment.dart';

/// RevenueCat configuration and initialization
class RevenueCatConfig {
  static const String _apiKeyIOS = 'appl_GYKVUlsXcYKImeWZJIHagwyNQdB'; // Your actual RevenueCat iOS API key
  static const String _apiKeyAndroid = 'goog_JPOFHCUywzSEXQZsqqDStlpjDGm'; // Your actual RevenueCat Android API key
  static const String _apiKeyWeb = 'appl_GYKVUlsXcYKImeWZJIHagwyNQdB'; // Use iOS key for web platform

  // RevenueCat Web Billing configuration
  static const String? _webProjectId = 'proj52c6ecd4'; // Your RevenueCat project ID
  static const String? _webApiKey = null; // Optional: Web API key for advanced features

  /// RevenueCat product identifiers for subscription packages
  /// App Store Connect: Full IDs with .animalia segment (no character limit)
  /// Google Play Console: Shortened IDs (40 character limit)
  static const Map<String, String> productIdsIOS = {
    'bronze_monthly': 'ro.animaliaprogramari.animalia.freelancer.monthly',
    'bronze_annual': 'ro.animaliaprogramari.animalia.freelancer.annual2',

    'silver_monthly': 'ro.animaliaprogramari.animalia.team.monthly',
    'silver_annual': 'ro.animaliaprogramari.animalia.team.annual',

    'gold_monthly': 'ro.animaliaprogramari.animalia.enterprise.monthly',
    'gold_annual': 'ro.animaliaprogramari.animalia.enterprise.annual',
  };

  static const Map<String, String> productIdsAndroid = {
    'silver_monthly': 'ro.animaliaprogramari.silver.monthly',
    'bronze_monthly': 'ro.animaliaprogramari.bronze.monthly',
    'gold_monthly': 'ro.animaliaprogramari.gold.monthly',

    'bronze_annual': 'ro.animaliaprogramari.bronze.annual',
    'silver_annual': 'ro.animaliaprogramari.silver.annual',
    'gold_annual': 'ro.animaliaprogramari.gold.annual',
  };

  /// Web product IDs for RevenueCat Web Billing
  /// These should match the RevenueCat Product IDs from your RevenueCat dashboard
  /// Format: prod[alphanumeric] - these are the actual RevenueCat Product IDs
  static const Map<String, String> productIdsWeb = {
    'bronze_monthly': 'prod0c00a8266', // animalia_freelancer_monthly
    'bronze_annual': 'prodbcd4161e68', // TODO: Get annual product ID from RevenueCat dashboard

    // TODO: Replace these package keys with proper RevenueCat product IDs (format: prod[alphanumeric])
    // These are currently package keys, not product IDs, which is why Team/Enterprise purchases fail
    'silver_monthly': 'prod68b783ae17', // TODO: Get from RevenueCat dashboard
    'silver_annual': 'prod26394c814a', // TODO: Get from RevenueCat dashboard

    'gold_monthly': 'prod8db2f9f1d1', // TODO: Get from RevenueCat dashboard
    'gold_annual': 'prodd285830a94', // TODO: Get from RevenueCat dashboard
  };

  /// Get platform-specific product IDs
  static Map<String, String> get productIds {
    if (kIsWeb) {
      return productIdsWeb;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return productIdsIOS;
    } else {
      return productIdsAndroid;
    }
  }

  /// RevenueCat entitlement identifiers
  static const Map<String, String> entitlementIds = {
    'premium_access': 'premium_access',
  };

  /// Initialize RevenueCat SDK
  static Future<void> initialize() async {
    try {
      // Configure RevenueCat
      await Purchases.setLogLevel(
        EnvironmentConfig.isDebugMode ? LogLevel.debug : LogLevel.info,
      );

      // Get the appropriate API key based on platform
      final apiKey = _getApiKey();
      
      // Initialize with user ID from authentication
      await Purchases.configure(
        PurchasesConfiguration(apiKey),
      );

      // Enable debug logs in development
      if (EnvironmentConfig.isDevelopment) {
        await Purchases.setLogLevel(LogLevel.verbose);
      }

    } catch (e) {
      rethrow;
    }
  }

  /// Get the appropriate API key for the current platform
  static String _getApiKey() {
    if (kIsWeb) {
      return _apiKeyWeb;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return _apiKeyIOS;
    } else {
      return _apiKeyAndroid;
    }
  }

  /// Set user ID for RevenueCat
  static Future<void> setUserId(String userId) async {
    try {
      await Purchases.logIn(userId);
    } catch (e) {
      rethrow;
    }
  }

  /// Log out user from RevenueCat
  static Future<void> logOut() async {
    try {
      await Purchases.logOut();
    } catch (e) {
      rethrow;
    }
  }

  /// Get offering for subscription packages
  static Future<Offering?> getSubscriptionOffering() async {
    try {
      final offerings = await Purchases.getOfferings();
      return offerings.current;
    } catch (e) {
      return null;
    }
  }

  /// Get customer info
  static Future<CustomerInfo?> getCustomerInfo() async {
    try {
      return await Purchases.getCustomerInfo();
    } catch (e) {
      return null;
    }
  }

  /// Purchase a package
  static Future<CustomerInfo?> purchasePackage(Package package) async {
    try {
      final customerInfo = await Purchases.purchasePackage(package);
      return customerInfo;
    } catch (e) {
      rethrow;
    }
  }

  /// Restore purchases
  static Future<CustomerInfo?> restorePurchases() async {
    try {
      return await Purchases.restorePurchases();
    } catch (e) {
      rethrow;
    }
  }

  /// Check if user has active entitlement
  static bool hasActiveEntitlement(CustomerInfo customerInfo, String entitlementId) {
    final entitlement = customerInfo.entitlements.active[entitlementId];
    return entitlement != null && entitlement.isActive;
  }

  /// Get active subscription tier from customer info
  static String? getActiveSubscriptionTier(CustomerInfo customerInfo) {
    for (final entry in entitlementIds.entries) {
      if (hasActiveEntitlement(customerInfo, entry.value)) {
        return entry.key;
      }
    }
    return null;
  }

  /// Check if user is in trial period
  static bool isInTrialPeriod(CustomerInfo customerInfo, String entitlementId) {
    final entitlement = customerInfo.entitlements.active[entitlementId];
    if (entitlement == null) return false;
    
    // Check if the entitlement is in trial period
    return entitlement.periodType == PeriodType.trial;
  }

  /// Get trial end date
  static String? getTrialEndDate(CustomerInfo customerInfo, String entitlementId) {
    final entitlement = customerInfo.entitlements.active[entitlementId];
    if (entitlement == null) return null;

    return entitlement.expirationDate;
  }

  /// Get subscription end date
  static String? getSubscriptionEndDate(CustomerInfo customerInfo, String entitlementId) {
    final entitlement = customerInfo.entitlements.active[entitlementId];
    if (entitlement == null) return null;

    return entitlement.expirationDate;
  }

  /// Validate API keys are configured
  static bool areApiKeysConfigured() {
    // Validate your actual RevenueCat API keys
    final bool hasValidKeys = _apiKeyIOS.isNotEmpty &&
           _apiKeyAndroid.isNotEmpty &&
           _apiKeyWeb.isNotEmpty &&
           _apiKeyIOS.startsWith('appl_') &&
           _apiKeyAndroid.startsWith('goog_') &&
           _apiKeyWeb.startsWith('appl_') &&
           _apiKeyIOS.length > 20 &&
           _apiKeyAndroid.length > 20 &&
           _apiKeyWeb.length > 20;

    return hasValidKeys;
  }

  /// Get the appropriate API key for the current platform
  static String getApiKey() {
    if (kIsWeb) {
      return _apiKeyWeb;
    } else if (defaultTargetPlatform == TargetPlatform.iOS) {
      return _apiKeyIOS;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      return _apiKeyAndroid;
    } else {
      throw UnsupportedError('Unsupported platform for RevenueCat');
    }
  }

  /// Get RevenueCat Web project ID
  static String? get webProjectId => _webProjectId;

  /// Get RevenueCat Web API key
  static String? get webApiKey => _webApiKey;

  /// Check if web billing is configured
  static bool get isWebBillingConfigured => _webProjectId != null && _webProjectId!.isNotEmpty;
}
