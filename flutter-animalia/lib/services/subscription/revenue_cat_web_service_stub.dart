import 'package:flutter/foundation.dart';

import '../../models/salon_subscription.dart';
import '../../utils/debug_logger.dart';
import 'revenue_cat_config.dart';

/// Web package model for RevenueCat Web Billing (stub for non-web platforms)
class WebPackage {
  final String id;
  final String name;
  final double priceAmount;
  final String currency;
  final SubscriptionTier tier;
  final String billing;
  final String description;

  WebPackage({
    required this.id,
    required this.name,
    required this.priceAmount,
    required this.currency,
    required this.tier,
    required this.billing,
    required this.description,
  });

  /// Get formatted price string
  String get priceString => '${priceAmount.toStringAsFixed(0)} $currency';
}

/// RevenueCat Web Billing service stub for non-web platforms
/// This provides the same interface but with no-op implementations
class RevenueCatWebService {
  static const String _baseUrl = '/api/subscriptions';

  // RevenueCat Web Billing configuration (stub)
  static String? _revenueCatProjectId;
  static String? _revenueCatWebApiKey;

  /// Available web packages (empty for non-web platforms)
  static List<WebPackage> get _availablePackages => [];

  /// Initialize RevenueCat Web service (no-op for non-web platforms)
  static Future<void> initialize() async {
    DebugLogger.logInit('⚠️ RevenueCat Web service called on non-web platform - using stub');
  }

  /// Check if RevenueCat Web is properly configured (always false for non-web)
  static bool _isConfigured() => false;

  /// Set user ID for RevenueCat Web (no-op for non-web platforms)
  static Future<void> setUserId(String userId) async {
    DebugLogger.logInit('⚠️ RevenueCat Web setUserId called on non-web platform');
  }

  /// Get available web subscription packages (empty for non-web platforms)
  static Future<List<WebPackage>> getAvailablePackages() async {
    DebugLogger.logInit('⚠️ RevenueCat Web getAvailablePackages called on non-web platform');
    return [];
  }

  /// Purchase a subscription package via web (throws error on non-web platforms)
  static Future<SalonSubscription?> purchaseSubscription({
    required WebPackage package,
    required String salonId,
  }) async {
    throw Exception('Web purchase called on non-web platform');
  }

  /// Configure RevenueCat Web settings (no-op for non-web platforms)
  static void configure({
    required String projectId,
    String? webApiKey,
  }) {
    _revenueCatProjectId = projectId;
    _revenueCatWebApiKey = webApiKey;
    DebugLogger.logInit('⚠️ RevenueCat Web configure called on non-web platform');
  }

  /// Get customer info from RevenueCat Web (null for non-web platforms)
  static Future<Map<String, dynamic>?> getCustomerInfo() async {
    DebugLogger.logInit('⚠️ RevenueCat Web getCustomerInfo called on non-web platform');
    return null;
  }

  /// Restore purchases for web (null for non-web platforms)
  static Future<SalonSubscription?> restorePurchases(String salonId) async {
    DebugLogger.logInit('⚠️ RevenueCat Web restorePurchases called on non-web platform');
    return null;
  }

  /// Check if running on web platform (always false for stub)
  static bool get isWebPlatform => false;

  /// Get web-specific product IDs
  static Map<String, String> get webProductIds => RevenueCatConfig.productIdsWeb;

  /// Get all available packages (empty for non-web platforms)
  static Future<List<WebPackage>> getAllPackages() async {
    return getAvailablePackages();
  }

  /// Get configuration status (always false for stub)
  static bool get isConfigured => false;

  /// Get current project ID (null for stub)
  static String? get projectId => null;
}
