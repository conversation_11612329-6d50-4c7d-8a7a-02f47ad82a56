import 'package:flutter/foundation.dart';
import 'dart:js' as js;
import 'dart:js_util' as js_util;

import '../../models/salon_subscription.dart';
import '../../utils/debug_logger.dart';
import '../api_service.dart';
import '../auth/auth_service.dart';
import 'revenue_cat_config.dart';

/// Web package model for RevenueCat Web Billing
class WebPackage {
  final String id;
  final String name;
  final double priceAmount;
  final String currency;
  final SubscriptionTier tier;
  final String billing;
  final String description;

  WebPackage({
    required this.id,
    required this.name,
    required this.priceAmount,
    required this.currency,
    required this.tier,
    required this.billing,
    required this.description,
  });

  /// Get formatted price string
  String get priceString => '${priceAmount.toStringAsFixed(0)} $currency';
}

/// RevenueCat Web Billing service for web platform
/// Uses RevenueCat Web Purchase Links for subscription purchases
class RevenueCatWebService {
  static const String _baseUrl = '/api/subscriptions';

  // RevenueCat Web Billing configuration
  // These will be configured in RevenueCat dashboard
  static String? _revenueCatProjectId;
  static String? _revenueCatWebApiKey;

  /// Available web packages based on current subscription tiers
  static List<WebPackage> get _availablePackages => [
    // Freelancer tier packages
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['bronze_monthly']!,
      name: 'Freelancer Monthly',
      priceAmount: SubscriptionTier.freelancer.monthlyPrice,
      currency: 'RON',
      tier: SubscriptionTier.freelancer,
      billing: 'monthly',
      description: SubscriptionTier.freelancer.subtitle,
    ),
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['bronze_annual']!,
      name: 'Freelancer Annual',
      priceAmount: SubscriptionTier.freelancer.annualPrice,
      currency: 'RON',
      tier: SubscriptionTier.freelancer,
      billing: 'annual',
      description: '${SubscriptionTier.freelancer.subtitle} - 2 months free!',
    ),

    // Team tier packages
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['silver_monthly']!,
      name: 'Team Monthly',
      priceAmount: SubscriptionTier.team.monthlyPrice,
      currency: 'RON',
      tier: SubscriptionTier.team,
      billing: 'monthly',
      description: SubscriptionTier.team.subtitle,
    ),
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['silver_annual']!,
      name: 'Team Annual',
      priceAmount: SubscriptionTier.team.annualPrice,
      currency: 'RON',
      tier: SubscriptionTier.team,
      billing: 'annual',
      description: '${SubscriptionTier.team.subtitle} - 2 months free!',
    ),

    // Enterprise tier packages
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['gold_monthly']!,
      name: 'Enterprise Monthly',
      priceAmount: SubscriptionTier.enterprise.monthlyPrice,
      currency: 'RON',
      tier: SubscriptionTier.enterprise,
      billing: 'monthly',
      description: SubscriptionTier.enterprise.subtitle,
    ),
    WebPackage(
      id: RevenueCatConfig.productIdsWeb['gold_annual']!,
      name: 'Enterprise Annual',
      priceAmount: SubscriptionTier.enterprise.annualPrice,
      currency: 'RON',
      tier: SubscriptionTier.enterprise,
      billing: 'annual',
      description: '${SubscriptionTier.enterprise.subtitle} - 2 months free!',
    ),
  ];

  /// Initialize RevenueCat Web service
  static Future<void> initialize() async {
    if (!kIsWeb) {
      DebugLogger.logInit('⚠️ RevenueCat Web service called on non-web platform');
      return;
    }

    try {
      DebugLogger.logInit('🔄 Initializing RevenueCat Web service...');

      // Validate configuration
      if (!_isConfigured()) {
        DebugLogger.logInit('⚠️ RevenueCat Web not configured - missing project ID or API key');
        DebugLogger.logInit('ℹ️ Web purchases will use fallback configuration');
      } else {
        DebugLogger.logInit('✅ RevenueCat Web configuration found');
      }

      DebugLogger.logInit('✅ RevenueCat Web service initialized with ${_availablePackages.length} packages');
    } catch (e) {
      DebugLogger.logInit('❌ Failed to initialize RevenueCat Web: $e');
      rethrow;
    }
  }

  /// Check if RevenueCat Web is properly configured
  static bool _isConfigured() {
    return _revenueCatProjectId != null &&
           _revenueCatProjectId!.isNotEmpty;
    // Note: Web API key is optional for basic web billing functionality
  }

  /// Set user ID for RevenueCat Web
  static Future<void> setUserId(String userId) async {
    if (!kIsWeb) return;

    try {
      DebugLogger.logInit('✅ RevenueCat Web user ID set: $userId');
      // User ID will be passed to purchase links as a parameter
    } catch (e) {
      DebugLogger.logInit('❌ Failed to set RevenueCat Web user ID: $e');
      rethrow;
    }
  }

  /// Get available web subscription packages
  static Future<List<WebPackage>> getAvailablePackages() async {
    if (!kIsWeb) return [];

    try {
      DebugLogger.logInit('🔍 Loading web packages...');

      // Return configured packages based on subscription tiers
      final packages = _availablePackages;

      DebugLogger.logInit('📦 Total web packages found: ${packages.length}');
      for (final package in packages) {
        DebugLogger.logInit('  - ${package.name}: ${package.priceString} (${package.billing})');
      }

      return packages;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get web packages: $e');
      return [];
    }
  }

  /// Purchase a subscription package via web
  static Future<SalonSubscription?> purchaseSubscription({
    required WebPackage package,
    required String salonId,
  }) async {
    if (!kIsWeb) {
      throw Exception('Web purchase called on non-web platform');
    }

    try {
      // Get the current user ID for RevenueCat customer identification
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        throw Exception('User not authenticated - cannot purchase subscription');
      }

      DebugLogger.logInit('🔄 Starting web subscription purchase');
      DebugLogger.logInit('👤 User ID: $userId');
      DebugLogger.logInit('🏢 Salon ID: $salonId');
      DebugLogger.logInit('📦 Package identifier: ${package.id}');
      DebugLogger.logInit('📦 Package name: ${package.name}');
      DebugLogger.logInit('📦 Price: ${package.priceString}');

      // Generate RevenueCat Web Purchase Link using user ID as customer identifier
      final purchaseUrl = _generatePurchaseLink(package, userId);
      DebugLogger.logInit('🔗 Generated purchase URL: $purchaseUrl');

      // Open the purchase link in a new window/tab
      DebugLogger.logInit('🌐 Opening RevenueCat checkout in new window...');
      js.context.callMethod('open', [purchaseUrl, '_blank']);

      DebugLogger.logInit('✅ Web subscription purchase initiated: ${package.tier.name}');
      DebugLogger.logInit('ℹ️ Purchase completion will be handled by RevenueCat webhooks');

      // Return null - actual subscription will be created via webhook
      // The UI should handle this by showing a "processing" state
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Web purchase failed: $e');
      rethrow;
    }
  }

  /// Generate RevenueCat Web Purchase Link
  /// Uses the proper Web Purchase Links format: https://pay.rev.cat/<token>/<user_id>
  static String _generatePurchaseLink(WebPackage package, String userId) {
    // RevenueCat Web Purchase Links are generated in the RevenueCat dashboard
    // Format: https://pay.rev.cat/<generated_token>/<user_id>
    // You need to create these in RevenueCat Dashboard > Offerings > Web Purchase Link tab

    print('Generating Web Purchase Link for package: ${package.id}');
    // Web Purchase Link tokens from RevenueCat dashboard
    // Get these from: Dashboard > Offerings > [Your Offering] > Web Purchase Link tab
    final webPurchaseLinks = <String, String>{
      // Bronze/Freelancer tier products
      'prod0c00a8266': kDebugMode ? 'khakgmhpmveyylyi' : 'dlnqputxoeuibdlt', // Freelancer Monthly
      'prodbcd4161e68': kDebugMode ? 'khakgmhpmveyylyi' : 'dlnqputxoeuibdlt', // Freelancer Annual (same offering)

      // Silver/Team tier products
      'prod68b783ae17': kDebugMode ? 'jvsmseregdxmffjm' : 'zywzsjlgyufqlbqp', // Team Monthly
      'prod26394c814a': kDebugMode ? 'jvsmseregdxmffjm' : 'zywzsjlgyufqlbqp', // Team Annual (same offering)

      // Gold/Enterprise tier products
      'prod8db2f9f1d1': kDebugMode ? 'xcbrkpwtrvshoiwf' : 'ryessxzpsdwifwfk', // Enterprise Monthly
      'prodd285830a94': kDebugMode ? 'xcbrkpwtrvshoiwf' : 'ryessxzpsdwifwfk', // Enterprise Annual (same offering)
    };

    final linkToken = webPurchaseLinks[package.id];
    if (linkToken == null) {
      throw Exception('Web Purchase Link not configured for product: ${package.id}');
    }

    // Construct the Web Purchase Link URL
    final environment = kDebugMode ? 'sandbox/' : '';
    final baseUrl = 'https://pay.rev.cat/$environment$linkToken/$userId';
    // Debug logging
    DebugLogger.logInit('🔗 Generated RevenueCat Web Purchase Link:');
    DebugLogger.logInit('   - Product ID: ${package.id}');
    DebugLogger.logInit('   - Link Token: $linkToken');
    DebugLogger.logInit('   - User ID: $userId');
    DebugLogger.logInit('   - Full URL: $baseUrl');

    return baseUrl;
  }

  /// Configure RevenueCat Web settings
  static void configure({
    required String projectId,
    String? webApiKey,
  }) {
    _revenueCatProjectId = projectId;
    _revenueCatWebApiKey = webApiKey;
    DebugLogger.logInit('✅ RevenueCat Web configured with project ID: $projectId');
  }

  /// Get customer info from RevenueCat Web
  static Future<Map<String, dynamic>?> getCustomerInfo() async {
    if (!kIsWeb) return null;

    try {
      // In a real implementation, this would query RevenueCat's REST API
      // For now, we rely on the backend to sync subscription status via webhooks
      DebugLogger.logInit('ℹ️ Customer info managed via backend webhook integration');
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get web customer info: $e');
      return null;
    }
  }

  /// Restore purchases for web
  static Future<SalonSubscription?> restorePurchases(String salonId) async {
    if (!kIsWeb) return null;

    try {
      DebugLogger.logInit('🔄 Attempting to restore web purchases for salon: $salonId');

      // For web purchases, restoration is handled by checking backend subscription status
      // RevenueCat webhooks will have already synced any active subscriptions
      final response = await ApiService.get('$_baseUrl/salon/$salonId/current');

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        DebugLogger.logInit('✅ Found existing subscription: ${subscription.tier.name}');
        return subscription;
      }

      DebugLogger.logInit('ℹ️ No existing subscription found for restoration');
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to restore web purchases: $e');
      return null; // Don't rethrow - restoration failure shouldn't break the app
    }
  }

  /// Check if running on web platform
  static bool get isWebPlatform => kIsWeb;

  /// Get web-specific product IDs
  static Map<String, String> get webProductIds => RevenueCatConfig.productIdsWeb;

  /// Get all available packages (for compatibility with existing code)
  static Future<List<WebPackage>> getAllPackages() async {
    return getAvailablePackages();
  }

  /// Get configuration status
  static bool get isConfigured => _isConfigured();

  /// Get current project ID (for debugging)
  static String? get projectId => _revenueCatProjectId;
}
