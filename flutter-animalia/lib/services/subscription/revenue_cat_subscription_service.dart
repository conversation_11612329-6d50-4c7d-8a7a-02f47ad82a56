import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../../models/salon_subscription.dart';
import '../../utils/debug_logger.dart';
import '../api_service.dart';
import 'revenue_cat_config.dart';
import 'revenue_cat_web_service.dart' show RevenueCatWebService, WebPackage;

/// Production RevenueCat subscription service
class RevenueCatSubscriptionService {
  static const String _baseUrl = '/api/subscriptions';

  /// Initialize RevenueCat subscription service
  static Future<void> initialize() async {
    try {
      DebugLogger.logInit('🔄 Initializing RevenueCat subscription service...');

      // Check if API keys are configured
      if (!RevenueCatConfig.areApiKeysConfigured()) {
        DebugLogger.logInit('⚠️ RevenueCat API keys not configured, using mock mode');
        return;
      }

      // Use web service for web platform, mobile service for others
      if (kIsWeb) {
        // Configure RevenueCat Web service
        if (RevenueCatConfig.isWebBillingConfigured) {
          DebugLogger.logInit('🔧 Configuring RevenueCat Web with project ID: ${RevenueCatConfig.webProjectId}');
          RevenueCatWebService.configure(
            projectId: RevenueCatConfig.webProjectId!,
            webApiKey: RevenueCatConfig.webApiKey,
          );
        } else {
          DebugLogger.logInit('❌ RevenueCat Web billing not configured - missing project ID');
        }
        await RevenueCatWebService.initialize();
        DebugLogger.logInit('✅ RevenueCat Web service initialized');
      } else {
        // Configure RevenueCat for mobile
        final configuration = PurchasesConfiguration(RevenueCatConfig.getApiKey());
        await Purchases.configure(configuration);
        DebugLogger.logInit('✅ RevenueCat mobile service initialized');
      }
    } catch (e) {
      DebugLogger.logInit('❌ Failed to initialize RevenueCat: $e');
      rethrow;
    }
  }

  /// Set user ID for RevenueCat
  static Future<void> setUserId(String userId) async {
    try {
      if (!RevenueCatConfig.areApiKeysConfigured()) {
        DebugLogger.logInit('⚠️ RevenueCat not configured, skipping user ID setup');
        return;
      }

      if (kIsWeb) {
        await RevenueCatWebService.setUserId(userId);
      } else {
        await Purchases.logIn(userId);
      }
      DebugLogger.logInit('✅ RevenueCat user ID set: $userId');
    } catch (e) {
      DebugLogger.logInit('❌ Failed to set RevenueCat user ID: $e');
      rethrow;
    }
  }

  /// Get available subscription packages
  static Future<List<Package>> getAvailablePackages() async {
    try {
      if (!RevenueCatConfig.areApiKeysConfigured()) {
        DebugLogger.logInit('⚠️ RevenueCat not configured, returning empty packages');
        return [];
      }

      // Web platform uses different package type
      if (kIsWeb) {
        DebugLogger.logInit('⚠️ Web platform detected - use getAvailableWebPackages() instead');
        return [];
      }

      final offerings = await Purchases.getOfferings();
      DebugLogger.logInit('🔍 RevenueCat offerings: ${offerings.all.keys.toList()}');

      if (offerings.all.isEmpty) {
        DebugLogger.logInit('⚠️ No offerings found');
        return [];
      }

      // Collect packages from ALL offerings, not just the default one
      final List<Package> allPackages = [];

      for (final offering in offerings.all.values) {
        DebugLogger.logInit('✅ Found ${offering.availablePackages.length} packages in offering: ${offering.identifier}');

        // Debug: Print all package details for this offering
        for (final package in offering.availablePackages) {
          DebugLogger.logInit('📦 Package: ${package.identifier}');
          DebugLogger.logInit('   Product ID: ${package.storeProduct.identifier}');
          DebugLogger.logInit('   Price: ${package.storeProduct.priceString}');
          DebugLogger.logInit('   Type: ${package.packageType}');
          DebugLogger.logInit('   Offering: ${offering.identifier}');
        }

        allPackages.addAll(offering.availablePackages);
      }

      DebugLogger.logInit('✅ Total packages collected from all offerings: ${allPackages.length}');
      DebugLogger.logInit('🔍 Available packages: ${allPackages.map((p) => p.identifier).join(', ')}');
      return allPackages;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get available packages: $e');
      return [];
    }
  }

  /// Get available web subscription packages
  static Future<List<WebPackage>> getAvailableWebPackages() async {
    if (!kIsWeb) return [];

    try {
      return await RevenueCatWebService.getAvailablePackages();
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get web packages: $e');
      return [];
    }
  }

  /// Get packages organized by subscription tier
  static Future<Map<SubscriptionTier, List<Package>>> getPackagesByTier() async {
    try {
      final packages = await getAvailablePackages();
      final Map<SubscriptionTier, List<Package>> packagesByTier = {};

      for (final package in packages) {
        final tier = getTierFromPackage(package);
        if (tier != null) {
          packagesByTier.putIfAbsent(tier, () => []).add(package);
        }
      }

      return packagesByTier;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to organize packages by tier: $e');
      return {};
    }
  }

  /// Purchase a subscription package
  static Future<SalonSubscription?> purchaseSubscription({
    required Package package,
    required String salonId,
  }) async {
    try {
      DebugLogger.logInit('🔄 Starting subscription purchase for salon: $salonId');
      DebugLogger.logInit('📦 Package identifier: ${package.identifier}');
      DebugLogger.logInit('📦 Product identifier: ${package.storeProduct.identifier}');
      DebugLogger.logInit('📦 Package type: ${package.packageType}');
      DebugLogger.logInit('📦 Price: ${package.storeProduct.priceString}');

      // Determine tier from product ID for debugging
      final tier = getTierFromPackage(package);
      DebugLogger.logShowcase('🎯 Target subscription tier: ${tier?.name ?? 'Unknown'}');

      if (!RevenueCatConfig.areApiKeysConfigured()) {
        throw Exception('RevenueCat not configured for production purchases');
      }

      // Web platform should use purchaseWebSubscription instead
      if (kIsWeb) {
        throw Exception('Use purchaseWebSubscription for web platform');
      }

      // Purchase through RevenueCat mobile
      final customerInfo = await Purchases.purchasePackage(package);
      
      // Verify purchase with backend
      final response = await ApiService.post('$_baseUrl/verify-purchase',
        body: {
          'salonId': salonId,
          'packageId': package.storeProduct.identifier, // Use App Store product ID, not RevenueCat package ID
          'customerInfo': _customerInfoToMap(customerInfo),
        },
      );

      DebugLogger.logInit('📄 Purchase verification response: ${response.success}');
      if (response.data != null) {
        DebugLogger.logInit('📄 Response data: ${response.data}');
      }
      if (response.error != null) {
        DebugLogger.logInit('❌ Response error: ${response.error}');
      }

      if (response.success && response.data != null) {
        try {
          final subscription = SalonSubscription.fromJson(response.data!);
          DebugLogger.logInit('✅ Successfully purchased and verified subscription:');
          DebugLogger.logInit('   - ID: ${subscription.id}');
          DebugLogger.logInit('   - Tier: ${subscription.tier.name}');
          DebugLogger.logInit('   - Status: ${subscription.status.name}');
          DebugLogger.logInit('   - Is Active: ${subscription.isActive}');
          DebugLogger.logInit('   - Is Trial: ${subscription.isTrialActive}');
          return subscription;
        } catch (parseError) {
          DebugLogger.logInit('❌ Failed to parse subscription response: $parseError');
          DebugLogger.logInit('❌ Raw response data: ${response.data}');
          throw Exception('Failed to parse subscription response: $parseError');
        }
      }

      throw Exception(response.error ?? 'Purchase verification failed');
    } catch (e) {
      DebugLogger.logInit('❌ Purchase failed: $e');
      
      // Check for specific Apple Store errors
      if (e.toString().contains('PRODUCT_NOT_AVAILABLE') || 
          e.toString().contains('NU ESTE DISPONIBIL')) {
        DebugLogger.logInit('🚨 Product not available in App Store - likely needs app review approval');
        throw Exception('Produsul selectat nu este disponibil. Vă rugăm să încercați din nou mai târziu.');
      }
      
      rethrow;
    }
  }

  /// Get current subscription for a salon
  static Future<SalonSubscription?> getCurrentSubscription(String salonId) async {
    try {
      final response = await ApiService.get('$_baseUrl/salon/$salonId/current');

      if (response.success && response.data != null) {
        return SalonSubscription.fromJson(response.data!);
      }

      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get current subscription: $e');
      return null;
    }
  }

  /// Purchase a web subscription package
  static Future<SalonSubscription?> purchaseWebSubscription({
    required WebPackage package,
    required String salonId,
  }) async {
    if (!kIsWeb) {
      throw Exception('Web purchase called on non-web platform');
    }

    return await RevenueCatWebService.purchaseSubscription(
      package: package,
      salonId: salonId,
    );
  }

  /// Get current subscription for user (applies to all their salons)
  static Future<SalonSubscription?> getUserSubscription() async {
    try {
      final response = await ApiService.get('$_baseUrl/user/current');

      if (response.success && response.data != null) {
        return SalonSubscription.fromJson(response.data!);
      }

      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get user subscription: $e');
      return null;
    }
  }

  /// Get customer info from RevenueCat
  static Future<CustomerInfo?> getCustomerInfo() async {
    try {
      if (!RevenueCatConfig.areApiKeysConfigured()) {
        return null;
      }

      if (kIsWeb) {
        // Web service returns different type, return null for now
        await RevenueCatWebService.getCustomerInfo();
        return null;
      }

      return await Purchases.getCustomerInfo();
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get customer info: $e');
      return null;
    }
  }

  /// Restore purchases
  static Future<SalonSubscription?> restorePurchases(String salonId) async {
    try {
      if (!RevenueCatConfig.areApiKeysConfigured()) {
        throw Exception('RevenueCat not configured for purchase restoration');
      }

      if (kIsWeb) {
        return await RevenueCatWebService.restorePurchases(salonId);
      }

      final customerInfo = await Purchases.restorePurchases();
      
      // Sync with backend
      final response = await ApiService.post('$_baseUrl/sync',
        body: {
          'salonId': salonId,
          'customerInfo': _customerInfoToMap(customerInfo),
        },
      );

      if (response.success && response.data != null) {
        return SalonSubscription.fromJson(response.data!);
      }
      
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to restore purchases: $e');
      rethrow;
    }
  }

  /// Check if salon has active subscription
  static Future<bool> hasActiveSubscription(String salonId) async {
    try {
      final subscription = await getCurrentSubscription(salonId);
      return subscription?.isActive ?? false;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check subscription status: $e');
      return false;
    }
  }

  /// Check if user can access a specific feature
  static Future<bool> canAccessFeature(String salonId, String feature) async {
    try {
      final response = await ApiService.get('$_baseUrl/salon/$salonId/feature/$feature');
      return response.success && (response.data == true);
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check feature access: $e');
      return false;
    }
  }

  /// Get subscription limits for a salon
  static Future<Map<String, int>> getSubscriptionLimits(String salonId) async {
    try {
      final response = await ApiService.get('$_baseUrl/salon/$salonId/limits');

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        return {
          'maxStaff': data['maxStaff'] ?? 0,
          'maxClients': data['maxClients'] ?? 0,
          'smsQuota': data['smsQuota'] ?? 0,
          'currentStaffCount': data['currentStaffCount'] ?? 0,
          'currentClientCount': data['currentClientCount'] ?? 0,
          'currentSmsUsage': data['currentSmsUsage'] ?? 0,
        };
      }

      return {
        'maxStaff': 0,
        'maxClients': 0,
        'smsQuota': 0,
        'currentStaffCount': 0,
        'currentClientCount': 0,
        'currentSmsUsage': 0,
      };
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get subscription limits: $e');
      return {
        'maxStaff': 0,
        'maxClients': 0,
        'smsQuota': 0,
        'currentStaffCount': 0,
        'currentClientCount': 0,
        'currentSmsUsage': 0,
      };
    }
  }

  /// Check if user can create salons based on their highest subscription tier
  static Future<bool> canUserCreateSalons() async {
    try {
      final response = await ApiService.get('$_baseUrl/user/can-create-salons');

      if (response.success && response.data != null) {
        final data = response.data as Map<String, dynamic>;
        return data['canCreate'] ?? false;
      }

      return false;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check salon creation permission: $e');
      return false;
    }
  }

  /// Create a free tier subscription for new users
  static Future<SalonSubscription?> createFreeSubscription() async {
    try {
      DebugLogger.logInit('🔄 Creating free tier subscription for user...');

      final response = await ApiService.post('$_baseUrl/create-free', body: {});

      if (response.success && response.data != null) {
        final subscription = SalonSubscription.fromJson(response.data!);
        DebugLogger.logInit('✅ Free subscription created successfully: ${subscription.tier.name}');
        return subscription;
      }

      DebugLogger.logInit('❌ Failed to create free subscription: ${response.error}');
      return null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to create free subscription: $e');
      return null;
    }
  }

  /// Check if user needs to select a subscription (new user without any subscription)
  static Future<bool> needsSubscriptionSelection() async {
    try {
      final subscription = await getUserSubscription();
      // User needs subscription selection if they have no subscription at all
      return subscription == null;
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check subscription selection need: $e');
      // If we can't check, assume they need selection to be safe
      return true;
    }
  }



  /// Log out user from subscription service
  static Future<void> logOut() async {
    try {
      if (RevenueCatConfig.areApiKeysConfigured()) {
        await Purchases.logOut();
      }
      DebugLogger.logInit('✅ Logged out from subscription service');
    } catch (e) {
      DebugLogger.logInit('❌ Failed to log out: $e');
    }
  }

  /// Helper method to determine subscription tier from package
  static SubscriptionTier? getTierFromPackage(Package package) {
    final productId = package.storeProduct.identifier;
    
    if (productId.contains('freelancer') || productId.contains('bronze')) {
      return SubscriptionTier.freelancer;
    } else if (productId.contains('team') || productId.contains('silver')) {
      return SubscriptionTier.team;
    } else if (productId.contains('enterprise') || productId.contains('gold')) {
      return SubscriptionTier.enterprise;
    }
    
    return null;
  }

  /// Helper method to convert CustomerInfo to Map for API calls
  static Map<String, dynamic> _customerInfoToMap(CustomerInfo customerInfo) {
    return {
      'originalAppUserId': customerInfo.originalAppUserId,
      'allPurchaseDates': customerInfo.allPurchaseDates,
      'entitlements': customerInfo.entitlements.all.map((key, value) =>
        MapEntry(key, {
          'identifier': value.identifier,
          'isActive': value.isActive,
          'willRenew': value.willRenew,
          'periodType': value.periodType.toString(),
          'latestPurchaseDate': value.latestPurchaseDate.toString(),
          'expirationDate': value.expirationDate.toString(),
          'productIdentifier': value.productIdentifier,
        })),
      'activeSubscriptions': customerInfo.activeSubscriptions,
      'allExpirationDates': customerInfo.allExpirationDates,
    };
  }
}
