import 'dart:convert';
import 'dart:io' show Platform;
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart' show debugPrint, kIsWeb;
// Temporarily commented out for macOS compatibility
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart'; // Removed Facebook auth
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class SocialAuthService {
  final FirebaseAuth _auth;

  // Temporarily commented out for macOS compatibility
  // final GoogleSignIn _googleSignIn = GoogleSignIn();

  SocialAuthService(this._auth);

  // Google Sign In
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign In process');

      // Create a GoogleAuthProvider credential
      final googleProvider = GoogleAuthProvider();

      // Add scopes (optional)
      googleProvider.addScope('https://www.googleapis.com/auth/userinfo.email');
      googleProvider.addScope(
          'https://www.googleapis.com/auth/userinfo.profile');

      // Set custom parameters for better UX
      googleProvider.setCustomParameters({
        'prompt': 'select_account'
      });

      // Sign in with popup/redirect for web or with Firebase directly for mobile
      if (kIsWeb) {
        // Web implementation
        debugPrint('Using web implementation for Google Sign In');
        try {
          // First try with popup
          final userCredential = await _auth.signInWithPopup(googleProvider);
          debugPrint(
              'Google Sign In successful with popup: ${userCredential.user
                  ?.email}');
          return userCredential;
        } catch (popupError) {
          debugPrint('Popup sign-in failed, trying redirect: $popupError');
          // If popup fails, try with redirect
          await _auth.signInWithRedirect(googleProvider);
          // Note: This will redirect the page, so we won't reach the code below
          // The result will be handled when the page loads back
          return null;
        }
      } else {
        // Mobile/desktop implementation
        debugPrint('Using Firebase Auth directly for Google Sign In');
        final userCredential = await _auth.signInWithProvider(googleProvider);
        debugPrint('Google Sign In successful: ${userCredential.user?.email}');
        return userCredential;
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Google Sign In: ${e.code} - ${e
          .message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during Google Sign In: $e');
      rethrow;
    }
  }

  // Apple Sign In
  Future<UserCredential?> signInWithApple() async {
    try {
      debugPrint('Starting Apple Sign In process');

      if (kIsWeb) {
        // Web implementation using Firebase directly
        debugPrint('Using web implementation for Apple Sign In');
        try {
          // Create an AppleAuthProvider
          final appleProvider = OAuthProvider('apple.com');

          // Add scopes
          appleProvider.addScope('email');
          appleProvider.addScope('name');

          // Set custom parameters for better UX
          appleProvider.setCustomParameters({
            'locale': 'en',
            'prompt': 'consent'
          });

          debugPrint('Attempting Apple Sign In with popup...');
          try {
            // Sign in with popup
            final userCredential = await _auth.signInWithPopup(appleProvider);
            debugPrint(
                'Apple Sign In successful with popup: ${userCredential.user?.email}');
            return userCredential;
          } catch (popupError) {
            debugPrint('Popup sign-in failed with detailed error: $popupError');

            // If popup fails, try with redirect
            debugPrint('Trying redirect method instead...');
            final redirectProvider = OAuthProvider('apple.com');
            redirectProvider.addScope('email');
            redirectProvider.addScope('name');
            redirectProvider.setCustomParameters({
              'locale': 'en',
              'prompt': 'consent'
            });
            await _auth.signInWithRedirect(redirectProvider);
            // Note: This will redirect the page, so we won't reach the code below
            // The result will be handled when the page loads back
            return null;
          }
        } catch (e) {
          debugPrint('Overall Apple Sign In error: $e');
          rethrow;
        }
      } else {
        // Mobile/desktop implementation - only allow on Apple platforms
        if (!Platform.isIOS && !Platform.isMacOS) {
          debugPrint('Apple Sign In is only available on Apple devices');
          throw Exception(
              'Sign in with Apple is only available on Apple devices');
        }
        // Mobile/desktop implementation using sign_in_with_apple package
        try {
          debugPrint('Using mobile/desktop implementation for Apple Sign In');
          debugPrint('Requesting Apple ID credential...');

          // Check if Apple Sign In is available on this device
          final isAvailable = await SignInWithApple.isAvailable();
          if (!isAvailable) {
            debugPrint('Sign in with Apple is not available on this device');
            throw Exception(
                'Sign in with Apple is not available on this device');
          }

          // For iOS, we need to generate a secure nonce
          final rawNonce = generateNonce();
          final nonce = sha256ofString(rawNonce);

          // Perform the sign-in request
          final credential = await SignInWithApple.getAppleIDCredential(
            scopes: [
              AppleIDAuthorizationScopes.email,
              AppleIDAuthorizationScopes.fullName,
            ],
            nonce: nonce,
          );

          debugPrint('Apple ID credential received: ${credential.email ??
              "email not provided"}');
          debugPrint(
              'Identity token length: ${credential.identityToken?.length ??
                  0}');

          // Verify we have the necessary tokens
          if (credential.identityToken == null) {
            debugPrint('Error: Identity token is null');
            throw Exception('Failed to get identity token from Apple');
          }

          // Create an OAuthCredential from the credential
          final oauthCredential = OAuthProvider('apple.com').credential(
            idToken: credential.identityToken!,
            rawNonce: rawNonce,
          );

          // Sign in to Firebase with the Apple credential
          debugPrint('Signing in to Firebase with Apple credential');
          final userCredential = await _auth.signInWithCredential(
              oauthCredential);
          debugPrint('Apple Sign In successful: ${userCredential.user?.email}');
          return userCredential;
        } catch (e) {
          debugPrint('Detailed Apple Sign In error on mobile: $e');
          rethrow;
        }
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Apple Sign In: ${e.code} - ${e
          .message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during Apple Sign In: $e');
      rethrow;
    }
  }


}


String generateNonce([int length = 32]) {
  const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
  final random = Random.secure();
  return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
}

/// Returns the sha256 hash of [input] in hex notation.
String sha256ofString(String input) {
  final bytes = utf8.encode(input);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
