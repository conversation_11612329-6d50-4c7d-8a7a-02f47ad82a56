import 'package:flutter/foundation.dart';

/// Centralized debug logging utility
/// Only logs API calls, errors, and initializations
class DebugLogger {
  static const bool _enableVerboseLogging = true; // Set to true for full logging
  
  /// Log API calls
  static void logApi(String message) {
    debugPrint('🌐 $message');
  }
  
  /// Log errors
  static void logError(String message) {
    debugPrint('❌ $message');
  }
  
  /// Log initializations
  static void logInit(String message) {
    debugPrint('🔄 $message');
  }
  
  /// Log success operations (only important ones)
  static void logSuccess(String message) {
    // debugPrint('✅ $message');
  }
  
  /// Log verbose messages (only if enabled)
  static void logVerbose(String message) {
    if (_enableVerboseLogging) {
      debugPrint('🔍 $message');
    }
  }
  
  /// Log warnings
  static void logWarning(String message) {
    debugPrint('⚠️ $message');
  }

  static void logShowcase(String message) {
    // debugPrint('🎯 $message');
  }
}