import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/subscription/revenue_cat_web_service.dart';
import '../../services/ui_notification_service.dart';
import '../../services/url_launcher_service.dart';
import '../../utils/debug_logger.dart';
import '../../widgets/common/custom_bottom_sheet.dart';

/// Screen for purchasing subscription packages
class SubscriptionPurchaseScreen extends StatefulWidget {
  final String salonId;
  final bool showTrialOption;

  const SubscriptionPurchaseScreen({
    super.key,
    required this.salonId,
    this.showTrialOption = true,
  });

  @override
  State<SubscriptionPurchaseScreen> createState() => _SubscriptionPurchaseScreenState();
}

class _SubscriptionPurchaseScreenState extends State<SubscriptionPurchaseScreen>
    with TickerProviderStateMixin {
  bool _isAnnualBilling = true; // Default to annual for 2 months free
  SubscriptionTier? _selectedTier;
  int _currentPageIndex = 1; // Start with Silver (index 1) as default
  bool _isShowingCancellationDialog = false; // Track if cancellation dialog is shown

  // Legal compliance URLs - Platform-specific requirements
  static const String _privacyPolicyUrl = 'https://animalia-programari.ro/privacy-policy.html';

  // Apple App Store EULA requirement - use Apple's standard EULA for iOS
  static const String _appleEulaUrl = 'https://www.apple.com/legal/internet-services/itunes/dev/stdeula/';

  // Custom Terms of Service for web and Android
  static const String _customTermsUrl = 'https://animalia-programari.ro/terms-of-service.html';

  // Subscription tiers in order
  static const List<SubscriptionTier> _tiers = [
    SubscriptionTier.freelancer,
    SubscriptionTier.team,
    SubscriptionTier.enterprise,
  ];

  @override
  void initState() {
    super.initState();

    // Set initial selected tier to Silver
    _selectedTier = SubscriptionTier.team;
    _currentPageIndex = 1;

    // Use addPostFrameCallback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeSubscriptions();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initializeSubscriptions() async {
    final subscriptionProvider = context.read<SubscriptionProvider>();

    if (!subscriptionProvider.isInitialized) {
      final userId = await AuthService.getCurrentUserId();
      if (userId != null) {
        await subscriptionProvider.initializeWithUserId(userId);
      }
    }

    // Load current subscription for user (applies to all their salons)
    await subscriptionProvider.loadUserSubscription();

    // Set the current subscription tier as selected and navigate to it
    if (mounted && subscriptionProvider.currentTier != null) {
      final currentTier = subscriptionProvider.currentTier!;
      final currentTierIndex = _tiers.indexOf(currentTier);
      setState(() {
        _selectedTier = currentTier;
        _currentPageIndex = currentTierIndex >= 0 ? currentTierIndex : 1;
      });
      // Update current page index
      if (currentTierIndex >= 0) {
        _currentPageIndex = currentTierIndex;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'Alege Planul Tău',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          if (subscriptionProvider.isLoading) {
            return const LoadingWidget(
              message: 'Se încarcă planurile...',
            );
          }

          // Don't show error state if we're showing cancellation dialog
          if (subscriptionProvider.hasError && !_isShowingCancellationDialog) {
            return _buildErrorState(subscriptionProvider.error!);
          }

          return _buildModernSubscriptionInterface(subscriptionProvider);
        },
      ),
    );
  }

  Widget _buildModernSubscriptionInterface(SubscriptionProvider subscriptionProvider) {
    return Column(
      children: [
        // Main content area - increased space for cards
        Expanded(
          child: Column(
            children: [
              // Subscription cards carousel - much more space for features
              Expanded(
                child: _buildSubscriptionCarousel(subscriptionProvider),
              ),

              // Purchase section with billing toggle
              _buildModernPurchaseSection(subscriptionProvider),
            ],
          ),
        ),
      ],
    );
  }



  /// Discrete bottom billing toggle
  Widget _buildBottomBillingToggle() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildDiscreteBillingOption(
              text: 'Lunar',
              isSelected: !_isAnnualBilling,
              onTap: () => setState(() => _isAnnualBilling = false),
            ),
          ),
          Expanded(
            child: _buildDiscreteBillingOption(
              text: 'Anual (2 luni gratuite)',
              isSelected: _isAnnualBilling,
              onTap: () => setState(() => _isAnnualBilling = true),
            ),
          ),
        ],
      ),
    );
  }

  /// Discrete billing option
  Widget _buildDiscreteBillingOption({
    required String text,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          boxShadow: isSelected ? [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Text(
          text,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            color: isSelected ? Colors.black87 : Colors.grey.shade600,
            fontSize: 14,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }



  /// Modern subscription cards list
  Widget _buildSubscriptionCarousel(SubscriptionProvider subscriptionProvider) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      itemCount: _tiers.length,
      itemBuilder: (context, index) {
        final tier = _tiers[index];
        final isSelected = _selectedTier == tier;
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedTier = tier;
                _currentPageIndex = index;
              });
            },
            child: _buildModernSubscriptionCard(
              tier,
              subscriptionProvider,
              isCenter: isSelected,
              isPopular: tier == SubscriptionTier.team,
            ),
          ),
        );
      },
    );
  }





  /// Modern subscription card with enhanced design
  Widget _buildModernSubscriptionCard(
    SubscriptionTier tier,
    SubscriptionProvider subscriptionProvider, {
    required bool isCenter,
    bool isPopular = false,
  }) {
    final isCurrentSubscription = subscriptionProvider.currentTier == tier;
    final tierColor = _getTierColor(tier);

    return Stack(
      children: [
        // Main card
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            border: isCenter
                ? Border.all(color: tierColor, width: 2)
                : Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: isCenter
                    ? tierColor.withValues(alpha: 0.2)
                    : Colors.black.withValues(alpha: 0.08),
                blurRadius: isCenter ? 20 : 12,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                _buildCardHeader(tier, tierColor, isCurrentSubscription),
                const SizedBox(height: 16),

                // Pricing
                _buildCardPricing(tier, tierColor, isCurrentSubscription: isCurrentSubscription),
                const SizedBox(height: 20),

                // Features
                _buildCardFeatures(tier),
              ],
            ),
          ),
        ),

        // Popular badge
        if (isPopular) _buildPopularBadge(),

        // Current subscription badge
        if (isCurrentSubscription) _buildCurrentBadge(tierColor),
      ],
    );
  }

  /// Card header with tier info
  Widget _buildCardHeader(SubscriptionTier tier, Color tierColor, bool isCurrentSubscription) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: tierColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            _getTierIcon(tier),
            color: tierColor,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tier.name,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              Text(
                isCurrentSubscription ? 'Planul tău activ' : tier.subtitle,
                style: TextStyle(
                  fontSize: 14,
                  color: isCurrentSubscription ? tierColor : Colors.grey.shade600,
                  fontWeight: isCurrentSubscription ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Card pricing section
  Widget _buildCardPricing(SubscriptionTier tier, Color tierColor, {bool isCurrentSubscription = false}) {
    final price = _isAnnualBilling ? tier.annualPrice : tier.monthlyPrice;
    final monthlyEquivalent = _isAnnualBilling ? tier.annualPrice / 12 : tier.monthlyPrice;
    final monthlySavings = _isAnnualBilling ? tier.monthlyPrice - monthlyEquivalent : 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_isAnnualBilling) ...[
          // For annual billing: Show monthly equivalent BIG
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${monthlyEquivalent.toInt()}',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'RON',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '/lună',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          // Show annual total and original monthly price
          Row(
            children: [
              Text(
                '${price.toInt()} RON/an',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '(${tier.monthlyPrice.toInt()} RON/lună)',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                  decoration: TextDecoration.lineThrough,
                ),
              ),
            ],
          ),
        ] else ...[
          // For monthly billing: Show monthly price BIG
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${price.toInt()}',
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'RON',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: tierColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '/lună',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ],

        if (_isAnnualBilling) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: isCurrentSubscription ? tierColor.withValues(alpha: 0.1) : Colors.green.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Economisești ${monthlySavings.toInt()} RON/lună • 2 luni gratuite!',
              style: TextStyle(
                color: isCurrentSubscription ? tierColor : Colors.green.shade700,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],

        // Special message for current subscription
        if (isCurrentSubscription) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  tierColor.withValues(alpha: 0.1),
                  tierColor.withValues(alpha: 0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: tierColor.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.favorite,
                  color: tierColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Mulțumim că ești cu noi!',
                    style: TextStyle(
                      color: tierColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      height: 1.3,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Card features section
  Widget _buildCardFeatures(SubscriptionTier tier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Key benefits highlight
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: _getTierColor(tier).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _getTierColor(tier).withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildBenefitItem(Icons.group, tier.maxStaff == -1 ? 'Nelimitat' : '${tier.maxStaff} groomeri', _getTierColor(tier)),
              _buildBenefitItem(Icons.sms, '${tier.smsQuota} SMS', _getTierColor(tier)),
              if (tier == SubscriptionTier.enterprise) _buildBenefitItem(Icons.business, 'Multi-salon', _getTierColor(tier))
              else _buildBenefitItem(Icons.pets, 'Clienți nelimitați', _getTierColor(tier)),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        // Features list
        Text(
          _getFeatureTitle(tier),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey.shade800,
          ),
        ),
        const SizedBox(height: 8),
        ...tier.features.map((feature) => Padding(
          padding: const EdgeInsets.only(bottom: 6),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: const EdgeInsets.only(top: 2),
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  color: Colors.green,
                  borderRadius: BorderRadius.circular(7),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 10,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  feature,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade700,
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  String _getFeatureTitle(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.freelancer:
        return 'Caracteristici incluse:';
      case SubscriptionTier.team:
        return 'Tot din Freelancer, plus:';
      case SubscriptionTier.enterprise:
        return 'Tot din Team, plus:';
      case SubscriptionTier.free:
        return 'Gratuit, luna de luna';
        throw UnimplementedError();
    }
  }

  Widget _buildBenefitItem(IconData icon, String text, Color color) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 6),
          Text(
            text,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Popular badge
  Widget _buildPopularBadge() {
    return Positioned(
      top: -8,
      right: 20,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.orange,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: const Text(
          'POPULAR',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );
  }

  /// Current subscription badge
  Widget _buildCurrentBadge(Color tierColor) {
    return Positioned(
      top: 16,
      left: 16,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: tierColor,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Text(
          'ACTIV',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Modern purchase section with billing toggle
  Widget _buildModernPurchaseSection(SubscriptionProvider subscriptionProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -8),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Billing toggle buttons at bottom
          _buildBottomBillingToggle(),
          const SizedBox(height: 16),

          // Purchase button
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _selectedTier != null && !subscriptionProvider.isLoading
                  ? () => _purchaseSubscription(subscriptionProvider)
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: _selectedTier != null
                    ? (_selectedTier == subscriptionProvider.currentTier 
                        ? Colors.grey.shade600 
                        : _getTierColor(_selectedTier!))
                    : Colors.grey.shade400,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 0,
              ),
              child: subscriptionProvider.isLoading
                  ? const SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      _selectedTier == subscriptionProvider.currentTier
                          ? '✨ Planul tău activ ✨'
                          : _getUpgradeButtonText(subscriptionProvider),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),

          const SizedBox(height: 12),

          // Apple App Store required subscription information
          _buildSubscriptionDisclosure(),

          const SizedBox(height: 12),

          // Compact footer links - Apple App Store compliance
          Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: _openTermsOfUseEula,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      _getPlatformTermsDisplayName(),
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  Text(
                    '•',
                    style: TextStyle(color: Colors.grey.shade400, fontSize: 10),
                  ),
                  TextButton(
                    onPressed: _openTermsAndConditions,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Termeni si Conditii',
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  Text(
                    '•',
                    style: TextStyle(color: Colors.grey.shade400, fontSize: 10),
                  ),
                  TextButton(
                    onPressed: () => _restorePurchases(subscriptionProvider),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Restaurează Achiziții',
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: _openPrivacyPolicy,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      'Politica Confidențialitate',
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Get tier-specific color
  Color _getTierColor(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.freelancer:
        return Colors.brown;
      case SubscriptionTier.team:
        return Colors.grey.shade600;
      case SubscriptionTier.enterprise:
        return Colors.amber.shade700;
      case SubscriptionTier.free:
        return Colors.green;

    }
  }

  /// Get tier-specific icon
  IconData _getTierIcon(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.freelancer:
        return Icons.star_border;
      case SubscriptionTier.team:
        return Icons.star_half;
      case SubscriptionTier.enterprise:
        return Icons.star;
      case SubscriptionTier.free:
        return Icons.money_off;
    }
  }

  /// Get appropriate button text for upgrade/downgrade
  String _getUpgradeButtonText(SubscriptionProvider subscriptionProvider) {
    if (subscriptionProvider.currentTier == null) {
      return 'Începe perioada de încercare gratuită';
    }

    final currentTier = subscriptionProvider.currentTier!;
    final selectedTier = _selectedTier!;

    final currentIndex = _tiers.indexOf(currentTier);
    final selectedIndex = _tiers.indexOf(selectedTier);

    if (selectedIndex > currentIndex) {
      return 'Upgrade la ${selectedTier.name}';
    } else if (selectedIndex < currentIndex) {
      return 'Schimbă la ${selectedTier.name}';
    } else {
      return 'Planul tău activ';
    }
  }

  /// Error state widget
  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'Eroare la încărcarea planurilor',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _initializeSubscriptions,
              child: const Text('Încearcă Din Nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Purchase subscription with enhanced cancellation handling
  Future<void> _purchaseSubscription(SubscriptionProvider subscriptionProvider) async {
    if (_selectedTier == null) return;

    // Add comprehensive logging for debugging
    DebugLogger.logInit('🛒 Starting subscription purchase from UI:');
    DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
    DebugLogger.logInit('   - Selected tier: ${_selectedTier!.name}');
    DebugLogger.logInit('   - Billing: ${_isAnnualBilling ? 'Annual' : 'Monthly'}');
    DebugLogger.logInit('   - Salon ID: ${widget.salonId}');

    // Handle web vs mobile packages differently
    if (kIsWeb) {
      // Use web packages for web platform
      final webPackage = subscriptionProvider.getWebPackage(_selectedTier!, isAnnual: _isAnnualBilling);
      if (webPackage == null) {
        DebugLogger.logInit('❌ Web package not found for tier: ${_selectedTier!.name}');
        DebugLogger.logInit('   - Available web packages: ${subscriptionProvider.availableWebPackages.length}');

        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: 'Pachetul selectat nu este disponibil',
        );
        return;
      }

      DebugLogger.logInit('✅ Web package found: ${webPackage.id}');
      DebugLogger.logInit('   - Package name: ${webPackage.name}');
      DebugLogger.logInit('   - Package price: ${webPackage.priceString}');

      // Purchase web package directly
      await _purchaseWebPackage(webPackage);
      return;
    }

    // Mobile platform - use regular packages
    final package = subscriptionProvider.getPackage(_selectedTier!, isAnnual: _isAnnualBilling);
    if (package == null) {
      DebugLogger.logInit('❌ Package not found for tier: ${_selectedTier!.name}');
      DebugLogger.logInit('   - Available packages: ${subscriptionProvider.availablePackages.length}');

      UINotificationService.showError(
        context: context,
        title: 'Eroare',
        message: 'Pachetul selectat nu este disponibil',
      );
      return;
    }

    DebugLogger.logInit('✅ Package found: ${package.storeProduct.identifier}');
    DebugLogger.logInit('   - Package title: ${package.storeProduct.title}');
    DebugLogger.logInit('   - Package price: ${package.storeProduct.priceString}');

    try {
      DebugLogger.logInit('🔄 Calling subscription provider purchase method...');

      final success = await subscriptionProvider.purchaseSubscription(
        package: package,
        salonId: widget.salonId,
      );

      DebugLogger.logInit('📋 Purchase result: $success');
      DebugLogger.logInit('   - Provider error: ${subscriptionProvider.error ?? 'None'}');
      DebugLogger.logInit('   - Current subscription: ${subscriptionProvider.currentSubscription?.tier.name ?? 'None'}');

      if (mounted) {
        if (success) {
          final newSubscription = subscriptionProvider.currentSubscription;
          final tierName = newSubscription?.tier.name ?? 'Unknown';

          DebugLogger.logInit('✅ Purchase successful - showing success message');
          UINotificationService.showSuccess(
            context: context,
            title: 'Abonament Activat',
            message: 'Abonamentul $tierName a fost activat cu succes!',
          );
          Navigator.of(context).pop(true);
        } else {
          // Check if the error is due to user cancellation
          final error = subscriptionProvider.error ?? '';
          DebugLogger.logInit('❌ Purchase failed with error: $error');

          if (_isPurchaseCancellation(error)) {
            DebugLogger.logInit('ℹ️ Detected user cancellation - showing cancellation dialog');
            _showCancellationMessage(subscriptionProvider);
          } else {
            DebugLogger.logInit('🚨 Showing error notification to user');
            UINotificationService.showError(
              context: context,
              title: 'Eroare la Achiziție',
              message: error.isNotEmpty ? error : 'Nu s-a putut activa abonamentul. Verifică conexiunea și încearcă din nou.',
            );
          }
        }
      }
    } on PlatformException catch (e) {
      if (mounted) {
        // Handle RevenueCat platform exceptions
        if (_isPurchaseCancellationException(e)) {
          _showCancellationMessage(subscriptionProvider);
        } else {
          UINotificationService.showError(
            context: context,
            title: 'Eroare la Achiziție',
            message: _getErrorMessageFromPlatformException(e),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        // Check if the error message indicates cancellation
        if (_isPurchaseCancellation(e.toString())) {
          _showCancellationMessage(subscriptionProvider);
        } else {
          UINotificationService.showError(
            context: context,
            title: 'Eroare Neașteptată',
            message: 'A apărut o eroare neașteptată. Te rugăm să încerci din nou.',
          );
        }
      }
    }
  }

  /// Purchase web package using subscription provider
  Future<void> _purchaseWebPackage(WebPackage webPackage) async {
    try {
      DebugLogger.logInit('🔄 Starting web package purchase: ${webPackage.name}');

      // Use subscription provider for consistent purchase flow
      final subscriptionProvider = Provider.of<SubscriptionProvider>(context, listen: false);

      final success = await subscriptionProvider.purchaseWebSubscription(
        package: webPackage,
        salonId: widget.salonId,
      );

      if (mounted) {
        if (success) {
          // Show success message - purchase completion will be handled by webhooks
          UINotificationService.showSuccess(
            context: context,
            title: 'Achiziție Inițiată',
            message: 'Achiziția pentru ${webPackage.tier.name} a fost inițiată. Completează plata în fereastra deschisă.',
          );

          // Navigate back - subscription status will be updated via webhooks
          Navigator.of(context).pop(false); // false indicates purchase initiated but not completed yet
        } else {
          UINotificationService.showError(
            context: context,
            title: 'Eroare la Achiziție',
            message: subscriptionProvider.error ?? 'Nu s-a putut iniția achiziția. Verifică conexiunea și încearcă din nou.',
          );
        }
      }
    } catch (e) {
      DebugLogger.logInit('❌ Web package purchase failed: $e');

      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare la Achiziție',
          message: 'Nu s-a putut iniția achiziția. Verifică conexiunea și încearcă din nou.',
        );
      }
    }
  }

  /// Restore purchases
  Future<void> _restorePurchases(SubscriptionProvider subscriptionProvider) async {
    try {
      final success = await subscriptionProvider.restorePurchases(widget.salonId);

      if (mounted) {
        if (success) {
          final restoredSubscription = subscriptionProvider.currentSubscription;
          final tierName = restoredSubscription?.tier.name ?? 'Unknown';

          UINotificationService.showSuccess(
            context: context,
            title: 'Achiziții Restaurate',
            message: 'Abonamentul $tierName a fost restaurat cu succes!',
          );
          Navigator.of(context).pop(true);
        } else {
          UINotificationService.showInfo(
            context: context,
            title: 'Informație',
            message: 'Nu au fost găsite achiziții de restaurat pentru acest cont.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare la Restaurare',
          message: 'Nu s-au putut restaura achizițiile. Verifică conexiunea și încearcă din nou.',
        );
      }
    }
  }

  /// Open privacy policy
  Future<void> _openPrivacyPolicy() async {
    final success = await UrlLauncherService.openWebUrl(_privacyPolicyUrl);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(
        context,
        'politica de confidențialitate',
      );
    }
  }

  /// Open terms and conditions - uses platform-specific terms
  Future<void> _openTermsAndConditions() async {
    // Use the same platform-specific logic as EULA
    final String termsUrl = _getPlatformSpecificTermsUrl();
    final success = await UrlLauncherService.openWebUrl(termsUrl);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(
        context,
        'termenii și condițiile',
      );
    }
  }

  /// Open Terms of Use (EULA) - Platform-specific implementation
  Future<void> _openTermsOfUseEula() async {
    // Use Apple's standard EULA for iOS, custom terms for other platforms
    final String termsUrl = _getPlatformSpecificTermsUrl();
    final String platformName = _getPlatformTermsDisplayName();

    final success = await UrlLauncherService.openWebUrl(termsUrl);
    if (!success && mounted) {
      UrlLauncherService.showLaunchError(
        context,
        platformName,
      );
    }
  }

  /// Get platform-specific terms URL
  String _getPlatformSpecificTermsUrl() {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      // Use Apple's standard EULA for iOS App Store compliance
      return _appleEulaUrl;
    } else {
      // Use custom terms for Android and Web
      return _customTermsUrl;
    }
  }

  /// Get platform-specific display name for terms
  String _getPlatformTermsDisplayName() {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      return 'Terms of Use (EULA)';
    } else {
      return 'Terms of Service';
    }
  }

  /// Build subscription disclosure - Apple App Store requirement
  Widget _buildSubscriptionDisclosure() {
    if (_selectedTier == null) return const SizedBox.shrink();

    final tier = _selectedTier!;
    final price = _isAnnualBilling ? tier.annualPrice : tier.monthlyPrice;
    final period = _isAnnualBilling ? 'an' : 'lună';
    final duration = _isAnnualBilling ? '1 an' : '1 lună';

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informații Abonament:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            '• Titlu: ${tier.name} ${_isAnnualBilling ? 'Anual' : 'Lunar'}',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
          ),
          Text(
            '• Durată: $duration (auto-reînnoire)',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
          ),
          Text(
            '• Preț: ${price.toInt()} RON/$period',
            style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
          ),
          if (_isAnnualBilling)
            Text(
              '• Preț pe unitate: ~${(price / 12).toInt()} RON/lună',
              style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
            ),
          const SizedBox(height: 4),
          Text(
            'Abonamentul se reînnoiește automat. Poți anula oricând din setările App Store.',
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade500,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// Check if error indicates purchase cancellation
  bool _isPurchaseCancellation(String error) {
    final lowerError = error.toLowerCase();
    return lowerError.contains('cancelled') ||
           lowerError.contains('canceled') ||
           lowerError.contains('user_cancelled') ||
           lowerError.contains('purchase_cancelled') ||
           lowerError.contains('anulat') ||
           lowerError.contains('anulată');
  }

  /// Check if PlatformException indicates purchase cancellation
  bool _isPurchaseCancellationException(PlatformException e) {
    // Check error code
    if (e.code == 'PURCHASE_CANCELLED' ||
        e.code == 'USER_CANCELLED' ||
        e.code == '1') { // iOS error code for user cancellation
      return true;
    }

    // Check error details
    final details = e.details;
    if (details is Map) {
      final userCancelled = details['userCancelled'];
      if (userCancelled == true) return true;
    }

    // Check error message
    return _isPurchaseCancellation(e.message ?? '');
  }

  /// Get user-friendly error message from PlatformException
  String _getErrorMessageFromPlatformException(PlatformException e) {
    switch (e.code) {
      case 'NETWORK_ERROR':
        return 'Verifică conexiunea la internet și încearcă din nou.';
      case 'PAYMENT_PENDING':
        return 'Plata este în așteptare. Vei fi notificat când va fi procesată.';
      case 'INVALID_CREDENTIALS':
        return 'Credențialele Apple ID nu sunt valide.';
      case 'PRODUCT_NOT_AVAILABLE':
        return 'Produsul nu este disponibil în regiunea ta.';
      case 'INSUFFICIENT_PERMISSIONS':
        return 'Nu ai permisiunile necesare pentru această achiziție.';
      default:
        return e.message ?? 'A apărut o eroare neașteptată. Te rugăm să încerci din nou.';
    }
  }

  /// Show user-friendly cancellation message and clear error state
  void _showCancellationMessage(SubscriptionProvider subscriptionProvider) {
    // Set flag to prevent error state from showing
    setState(() {
      _isShowingCancellationDialog = true;
    });

    // Clear the error state immediately to prevent error UI from showing after dialog dismissal
    subscriptionProvider.clearErrorState();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        icon: Icon(
          Icons.info_outline,
          color: Colors.blue.shade600,
          size: 48,
        ),
        title: const Text(
          'Achiziție Anulată',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 20,
          ),
          textAlign: TextAlign.center,
        ),
        content: const Text(
          'Achiziția abonamentului a fost anulată de către utilizator. Poți încerca din nou oricând pentru a activa funcționalitățile premium sau poți continua să folosești aplicația cu planul curent disponibil.',
          style: TextStyle(
            fontSize: 16,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
            child: Text(
              'Am Înțeles',
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    ).then((_) {
      // Ensure flag is cleared when dialog is dismissed by any means
      if (mounted) {
        setState(() {
          _isShowingCancellationDialog = false;
        });
      }
    });
  }
}
