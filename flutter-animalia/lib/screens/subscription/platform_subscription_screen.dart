import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'subscription_purchase_screen.dart';
import 'web_subscription_purchase_screen.dart';

/// Platform-aware subscription screen that routes to the appropriate implementation
class PlatformSubscriptionScreen extends StatelessWidget {
  final String salonId;
  final bool showTrialOption;

  const PlatformSubscriptionScreen({
    super.key,
    required this.salonId,
    this.showTrialOption = true,
  });

  @override
  Widget build(BuildContext context) {
    // Use web-specific screen for web platform
    if (kIsWeb) {
      return WebSubscriptionPurchaseScreen(
        salonId: salonId,
      );
    }
    
    // Use mobile screen for iOS/Android
    return SubscriptionPurchaseScreen(
      salonId: salonId,
      showTrialOption: showTrialOption,
    );
  }
}
