import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/subscription/revenue_cat_web_service.dart';
import '../../utils/debug_logger.dart';


/// Web-specific subscription purchase screen using RevenueCat Web Billing
class WebSubscriptionPurchaseScreen extends StatefulWidget {
  final String salonId;

  const WebSubscriptionPurchaseScreen({
    super.key,
    required this.salonId,
  });

  @override
  State<WebSubscriptionPurchaseScreen> createState() => _WebSubscriptionPurchaseScreenState();
}

class _WebSubscriptionPurchaseScreenState extends State<WebSubscriptionPurchaseScreen> {
  List<WebPackage> _packages = [];
  bool _isLoading = true;
  String? _error;
  bool _isPurchasing = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _loadPackages();
    } else {
      _error = 'This screen is only available on web platform';
      _isLoading = false;
    }
  }

  Future<void> _loadPackages() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Get packages directly from RevenueCat Web Service
      final packages = await RevenueCatWebService.getAvailablePackages();

      setState(() {
        _packages = packages;
        _isLoading = false;
      });

      if (packages.isEmpty) {
        setState(() {
          _error = 'No subscription packages available. Please check your RevenueCat configuration.';
        });
      }
    } catch (e) {
      DebugLogger.logInit('❌ Failed to load web packages: $e');
      setState(() {
        _error = 'Failed to load subscription packages: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _purchasePackage(WebPackage package) async {
    if (_isPurchasing) return;

    try {
      setState(() {
        _isPurchasing = true;
      });

      DebugLogger.logInit('🔄 Starting web purchase for package: ${package.id}');

      // Use subscription provider for consistent purchase flow
      final subscriptionProvider = Provider.of<SubscriptionProvider>(context, listen: false);

      final success = await subscriptionProvider.purchaseWebSubscription(
        package: package,
        salonId: widget.salonId,
      );

      if (mounted) {
        if (success) {
          // Show processing message since purchase completion is handled by webhooks
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Purchase initiated for ${package.tier.name}. Complete your payment in the new window.'),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 5),
            ),
          );

          // Navigate back - user will see subscription status update via webhooks
          Navigator.of(context).pop(false); // false indicates purchase was initiated but not completed yet
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to initiate purchase: ${subscriptionProvider.error ?? 'Unknown error'}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      DebugLogger.logInit('❌ Web purchase failed: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to initiate purchase: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Choose Your Plan'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Stack(
        children: [
          _buildBody(),
          if (_isPurchasing)
            Container(
              color: Colors.black54,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading subscription plans...'),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPackages,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_packages.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No subscription plans available',
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Choose the perfect plan for your grooming business',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Upgrade anytime. Cancel anytime. All plans include a 2-month free trial.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ..._packages.map((package) => _buildPackageCard(package)),
          const SizedBox(height: 24),
          _buildWebBillingInfo(),
        ],
      ),
    );
  }

  Widget _buildPackageCard(WebPackage package) {
    final isAnnual = package.billing == 'annual';

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      package.tier.name,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      package.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      package.priceString,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      isAnnual ? 'per year' : 'per month',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (isAnnual) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  '2 months FREE',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
            ],
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isPurchasing ? null : () => _purchasePackage(package),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: _isPurchasing
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Choose This Plan',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWebBillingInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'Secure Web Payments',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              '• Powered by RevenueCat Web Billing\n'
              '• Secure payment processing via Stripe\n'
              '• Same subscription across all your devices\n'
              '• Easy subscription management\n'
              '• 2-month free trial included',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }


}
