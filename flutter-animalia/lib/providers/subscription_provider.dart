import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import '../models/salon_subscription.dart';
import '../providers/base_provider.dart';
import '../services/sms_quota_service.dart';
import '../services/subscription/revenue_cat_subscription_service.dart';
import '../services/subscription/revenue_cat_web_service.dart';
import '../services/subscription_limit_service.dart';
import '../utils/debug_logger.dart';

/// Provider for managing subscription state and operations
class SubscriptionProvider extends BaseProvider {
  SalonSubscription? _currentSubscription;
  List<Package> _availablePackages = [];
  List<WebPackage> _availableWebPackages = [];
  Map<SubscriptionTier, List<Package>> _packagesByTier = {};
  CustomerInfo? _customerInfo;
  bool _isInitialized = false;

  // Getters
  SalonSubscription? get currentSubscription => _currentSubscription;
  List<Package> get availablePackages => _availablePackages;
  List<WebPackage> get availableWebPackages => _availableWebPackages;
  Map<SubscriptionTier, List<Package>> get packagesByTier => _packagesByTier;
  CustomerInfo? get customerInfo => _customerInfo;
  @override
  bool get isInitialized => _isInitialized;

  // Subscription status checks
  bool get hasActiveSubscription => _currentSubscription?.isActive ?? false;
  bool get isInTrial => _currentSubscription?.isTrialActive ?? false;
  bool get isExpired => _currentSubscription?.isExpired ?? false;
  SubscriptionTier? get currentTier => _currentSubscription?.tier;

  /// Initialize subscription provider
  @override
  Future<void> initialize() async {
    // This method is called without userId, so we'll need to get it from auth
    // For now, we'll create a separate initializeWithUserId method
  }

  /// Initialize subscription provider with user ID
  Future<void> initializeWithUserId(String userId) async {
    if (_isInitialized) return;

    try {
      setLoading(true);

      // Initialize RevenueCat
      DebugLogger.logInit('🔄 Initializing with RevenueCat...');
      await RevenueCatSubscriptionService.initialize();
      await RevenueCatSubscriptionService.setUserId(userId);

      // Load available packages
      await _loadAvailablePackages();

      // Load customer info
      await _loadCustomerInfo();

      _isInitialized = true;
      DebugLogger.logInit('✅ Subscription provider initialized with RevenueCat');
    } catch (e) {
      setError('Failed to initialize subscriptions: $e');
      DebugLogger.logInit('❌ Subscription provider initialization failed: $e');
    } finally {
      setLoading(false);
    }
  }

  /// Load current subscription for a salon
  Future<void> loadCurrentSubscription(String salonId) async {
    try {
      setLoading(true);
      clearError();

      final subscription = await RevenueCatSubscriptionService.getCurrentSubscription(salonId);
      _currentSubscription = subscription;

      DebugLogger.logInit('✅ Current subscription loaded: ${subscription?.tier.name ?? 'None'}');
      notifyListeners();
    } catch (e) {
      setError('Failed to load subscription: $e');
      DebugLogger.logInit('❌ Failed to load current subscription: $e');
    } finally {
      setLoading(false);
    }
  }

  /// Load current subscription for user (applies to all their salons)
  Future<void> loadUserSubscription() async {
    try {
      setLoading(true);
      clearError();

      final subscription = await RevenueCatSubscriptionService.getUserSubscription();
      _currentSubscription = subscription;

      DebugLogger.logInit('✅ User subscription loaded: ${subscription?.tier.name ?? 'None'}');
      notifyListeners();
    } catch (e) {
      setError('Failed to load user subscription: $e');
      DebugLogger.logInit('❌ Failed to load user subscription: $e');
    } finally {
      setLoading(false);
    }
  }

  /// Purchase a subscription package
  Future<bool> purchaseSubscription({
    required Package package,
    required String salonId,
  }) async {
    try {
      setLoading(true);
      clearError();

      final previousTier = _currentSubscription?.tier;
      DebugLogger.logInit('🔄 Starting subscription purchase:');
      DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      DebugLogger.logInit('   - Previous tier: ${previousTier?.name ?? 'None'}');
      DebugLogger.logInit('   - Target product: ${package.storeProduct.identifier}');
      DebugLogger.logInit('   - Salon ID: $salonId');

      // Platform-specific purchase handling
      SalonSubscription? subscription;

      if (kIsWeb) {
        DebugLogger.logInit('🌐 Web platform detected - using web purchase flow');

        // For web, we need to convert Package to WebPackage or handle differently
        // Check if we have web packages available
        if (_availableWebPackages.isNotEmpty) {
          // Find matching web package by identifier
          final webPackage = _availableWebPackages.firstWhere(
            (wp) => wp.id == package.storeProduct.identifier,
            orElse: () {
              DebugLogger.logInit('⚠️ No matching web package found for: ${package.storeProduct.identifier}');
              DebugLogger.logInit('   Available web packages: ${_availableWebPackages.map((wp) => wp.id).join(', ')}');
              throw Exception('Web package not found for product: ${package.storeProduct.identifier}');
            },
          );

          DebugLogger.logInit('✅ Found matching web package: ${webPackage.name}');
          subscription = await RevenueCatSubscriptionService.purchaseWebSubscription(
            package: webPackage,
            salonId: salonId,
          );
        } else {
          DebugLogger.logInit('❌ No web packages available - this should not happen');
          throw Exception('Web packages not loaded. Please refresh and try again.');
        }
      } else {
        DebugLogger.logInit('📱 Mobile platform detected - using mobile purchase flow');
        subscription = await RevenueCatSubscriptionService.purchaseSubscription(
          package: package,
          salonId: salonId,
        );
      }

      if (subscription != null) {
        _currentSubscription = subscription;
        await _loadCustomerInfo(); // Refresh customer info

        // Clear subscription limits cache to force reload with new limits
        await _invalidateSubscriptionCache(salonId);

        notifyListeners();
        DebugLogger.logInit('✅ Subscription purchased successfully:');
        DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
        DebugLogger.logInit('   - Previous tier: ${previousTier?.name ?? 'None'}');
        DebugLogger.logInit('   - New tier: ${subscription.tier.name}');
        DebugLogger.logInit('   - Upgrade successful: ${previousTier != subscription.tier}');
        return true;
      }

      DebugLogger.logInit('❌ Purchase failed: No subscription returned from backend');
      DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      DebugLogger.logInit('   - This might be expected for web purchases (handled via webhooks)');

      // For web purchases, null return might be expected (handled via webhooks)
      if (kIsWeb) {
        DebugLogger.logInit('ℹ️ Web purchase initiated - completion handled via webhooks');
        return true; // Consider web purchase initiation as success
      }

      return false;
    } catch (e) {
      final errorString = e.toString();

      DebugLogger.logInit('❌ Purchase error details:');
      DebugLogger.logInit('   - Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
      DebugLogger.logInit('   - Error type: ${e.runtimeType}');
      DebugLogger.logInit('   - Error message: $errorString');
      DebugLogger.logInit('   - Salon ID: $salonId');
      DebugLogger.logInit('   - Package ID: ${package.storeProduct.identifier}');

      // Check if this is a user cancellation - don't treat as error
      if (isPurchaseCancellation(errorString)) {
        DebugLogger.logInit('ℹ️ Purchase cancelled by user: $e');
        setError('Purchase cancelled: $e'); // Set error for UI to detect cancellation
        return false;
      }

      // For actual errors, set error state
      setError('Purchase failed: $e');
      DebugLogger.logInit('❌ Purchase failed: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// Check if error indicates purchase cancellation
  bool isPurchaseCancellation(String error) {
    final lowerError = error.toLowerCase();
    return lowerError.contains('cancelled') ||
           lowerError.contains('canceled') ||
           lowerError.contains('user_cancelled') ||
           lowerError.contains('purchase_cancelled') ||
           lowerError.contains('anulat') ||
           lowerError.contains('anulată');
  }

  /// Purchase a web subscription package directly
  Future<bool> purchaseWebSubscription({
    required WebPackage package,
    required String salonId,
  }) async {
    if (!kIsWeb) {
      throw Exception('Web purchase called on non-web platform');
    }

    try {
      setLoading(true);
      clearError();

      DebugLogger.logInit('🔄 Starting web subscription purchase:');
      DebugLogger.logInit('   - Package: ${package.name}');
      DebugLogger.logInit('   - Tier: ${package.tier.name}');
      DebugLogger.logInit('   - Price: ${package.priceString}');
      DebugLogger.logInit('   - Salon ID: $salonId');

      final subscription = await RevenueCatSubscriptionService.purchaseWebSubscription(
        package: package,
        salonId: salonId,
      );

      if (subscription != null) {
        _currentSubscription = subscription;
        await _loadCustomerInfo(); // Refresh customer info

        // Clear subscription limits cache to force reload with new limits
        await _invalidateSubscriptionCache(salonId);

        notifyListeners();
        DebugLogger.logInit('✅ Web subscription purchased successfully: ${subscription.tier.name}');
        return true;
      }

      // For web purchases, null return is expected (handled via webhooks)
      DebugLogger.logInit('ℹ️ Web purchase initiated - completion handled via webhooks');
      return true; // Consider web purchase initiation as success
    } catch (e) {
      final errorString = e.toString();

      DebugLogger.logInit('❌ Web purchase error:');
      DebugLogger.logInit('   - Error: $errorString');
      DebugLogger.logInit('   - Package: ${package.name}');
      DebugLogger.logInit('   - Salon ID: $salonId');

      // Check if this is a user cancellation
      if (isPurchaseCancellation(errorString)) {
        DebugLogger.logInit('ℹ️ Web purchase cancelled by user');
        setError('Purchase cancelled: $e');
        return false;
      }

      setError('Web purchase failed: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// Restore purchases
  Future<bool> restorePurchases(String salonId) async {
    try {
      setLoading(true);
      clearError();

      final subscription = await RevenueCatSubscriptionService.restorePurchases(salonId);

      if (subscription != null) {
        _currentSubscription = subscription;
        await _loadCustomerInfo(); // Refresh customer info
        notifyListeners();
        DebugLogger.logInit('✅ Purchases restored successfully');
        return true;
      }

      DebugLogger.logInit('ℹ️ No purchases to restore');
      return false;
    } catch (e) {
      setError('Failed to restore purchases: $e');
      DebugLogger.logInit('❌ Failed to restore purchases: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  /// Check if user can access a specific feature
  Future<bool> canAccessFeature(String salonId, String feature) async {
    try {
      return await RevenueCatSubscriptionService.canAccessFeature(salonId, feature);
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check feature access: $e');
      return false;
    }
  }

  /// Get subscription limits
  Future<Map<String, int>> getSubscriptionLimits(String salonId) async {
    try {
      return await RevenueCatSubscriptionService.getSubscriptionLimits(salonId);
    } catch (e) {
      DebugLogger.logInit('❌ Failed to get subscription limits: $e');
      return {
        'maxStaff': 0,
        'maxClients': 0,
        'smsQuota': 0,
      };
    }
  }

  /// Check if user can create salons based on their highest subscription tier
  Future<bool> canUserCreateSalons() async {
    try {
      return await RevenueCatSubscriptionService.canUserCreateSalons();
    } catch (e) {
      DebugLogger.logInit('❌ Failed to check salon creation permission: $e');
      return false;
    }
  }

  /// Get package for a specific tier and billing period
  Package? getPackage(SubscriptionTier tier, {bool isAnnual = false}) {
    if (kIsWeb) {
      // For web platform, we shouldn't use this method
      // Use getWebPackage instead and handle web purchases directly
      DebugLogger.logInit('⚠️ getPackage called on web platform - use getWebPackage instead');
      return null;
    }

    // Mobile platform logic
    final packages = _packagesByTier[tier];
    if (packages == null || packages.isEmpty) return null;

    DebugLogger.logInit('🔍 Found ${packages.length} packages for tier: ${tier.name}');

    // Find the appropriate package based on billing period
    for (final package in packages) {
      // For RevenueCat packages, check product identifier
      final productId = package.storeProduct.identifier;
      if (isAnnual && productId.contains('annual')) {
        return package;
      } else if (!isAnnual && productId.contains('monthly')) {
        return package;
      }
    }
    DebugLogger.logInit('⚠️ No matching package found for tier: ${tier.name}');

    // Fallback to first available package
    return packages.first;
  }

  /// Get trial days remaining
  int get trialDaysRemaining => _currentSubscription?.trialDaysRemaining ?? 0;

  /// Get subscription days remaining
  int get subscriptionDaysRemaining => _currentSubscription?.subscriptionDaysRemaining ?? 0;

  /// Public method to clear error state (for UI components)
  void clearErrorState() {
    clearError();
  }

  /// Get web package for a specific tier and billing period
  WebPackage? getWebPackage(SubscriptionTier tier, {bool isAnnual = false}) {
    return _availableWebPackages.where((package) {
      return package.tier == tier &&
             ((isAnnual && package.billing == 'annual') ||
              (!isAnnual && package.billing == 'monthly'));
    }).firstOrNull;
  }



  /// Load available packages from subscription service
  Future<void> _loadAvailablePackages() async {
    try {
      if (kIsWeb) {
        // Load web packages
        _availableWebPackages = await RevenueCatSubscriptionService.getAvailableWebPackages();
        DebugLogger.logInit('✅ Loaded ${_availableWebPackages.length} web subscription packages with RevenueCat');
      } else {
        // Load mobile packages
        _availablePackages = await RevenueCatSubscriptionService.getAvailablePackages();
        _packagesByTier = await RevenueCatSubscriptionService.getPackagesByTier();
        DebugLogger.logInit('✅ Loaded ${_availablePackages.length} mobile subscription packages with RevenueCat');
      }
    } catch (e) {
      DebugLogger.logInit('❌ Failed to load packages: $e');
      rethrow;
    }
  }

  /// Load customer info from subscription service
  Future<void> _loadCustomerInfo() async {
    try {
      _customerInfo = await RevenueCatSubscriptionService.getCustomerInfo();
      DebugLogger.logInit('✅ Customer info loaded with RevenueCat');
    } catch (e) {
      DebugLogger.logInit('❌ Failed to load customer info: $e');
      // Don't throw here as this is not critical for basic functionality
    }
  }

  /// Refresh subscription data
  @override
  Future<void> refresh() async {
    // Base refresh without parameters
    await _loadAvailablePackages();
    await _loadCustomerInfo();
  }

  /// Refresh subscription data for a specific salon
  Future<void> refreshForSalon(String salonId) async {
    await loadCurrentSubscription(salonId);
    await _loadCustomerInfo();
  }

  /// Refresh subscription data for user (applies to all their salons)
  Future<void> refreshForUser() async {
    await loadUserSubscription();
    await _loadCustomerInfo();
  }

  /// Invalidate subscription cache after changes
  Future<void> _invalidateSubscriptionCache(String salonId) async {
    try {
      // Clear subscription limits cache to force reload with new limits
      SubscriptionLimitService.clearCache(salonId);
      DebugLogger.logInit('✅ Subscription cache invalidated for salon: $salonId');

      // Refresh SMS quota after subscription changes
      await SmsQuotaService.refreshAfterSubscriptionChange();
      DebugLogger.logInit('✅ SMS quota refreshed after subscription change');

      // Notify listeners that subscription data has changed
      // This will trigger widgets like UsageDashboard to refresh
      notifyListeners();
    } catch (e) {
      DebugLogger.logInit('⚠️ Failed to invalidate subscription cache: $e');
    }
  }

  /// Clear subscription data (for logout)
  @override
  void clear() {
    _currentSubscription = null;
    _availablePackages = [];
    _packagesByTier = {};
    _customerInfo = null;
    _isInitialized = false;
    super.clear(); // Call parent clear method
  }

  /// Log out from subscription service
  Future<void> logOut() async {
    try {
      await RevenueCatSubscriptionService.logOut();
      clear();
      DebugLogger.logInit('✅ Logged out from subscription service');
    } catch (e) {
      DebugLogger.logInit('❌ Failed to log out from subscription service: $e');
    }
  }
}
