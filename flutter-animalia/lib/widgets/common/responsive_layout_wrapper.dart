import 'package:flutter/material.dart';

/// A responsive layout wrapper that constrains content width on large screens
/// and provides appropriate padding for better UX on desktop/tablet devices
class ResponsiveLayoutWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsets? padding;
  final bool centerContent;

  const ResponsiveLayoutWrapper({
    super.key,
    required this.child,
    this.maxWidth = 1800.0,
    this.padding,
    this.centerContent = true,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding =  EdgeInsets.all(0.0);
    
    // Use LayoutBuilder for responsive behavior
    return LayoutBuilder(
      builder: (context, constraints) {
        // Apply width constraints based on screen size
        if (constraints.maxWidth > maxWidth) {
          // Large screens: center content with max width
          return Center(
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: maxWidth),
              child: Padding(
                padding: effectivePadding,
                child: child,
              ),
            ),
          );
        } else {
          // Small screens: use full width with padding
          return Padding(
            padding: effectivePadding,
            child: child,
          );
        }
      },
    );
  }
}

/// A responsive form wrapper specifically designed for forms
/// Provides optimal width constraints and padding for form elements
class ResponsiveFormWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsets? padding;

  const ResponsiveFormWrapper({
    super.key,
    required this.child,
    this.maxWidth = 480.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    // Remove width constraints - use ResponsiveLayoutWrapper without constraints
    return ResponsiveLayoutWrapper(
      padding: padding ?? EdgeInsets.zero,
      child: child,
    );
  }
}

/// A responsive card wrapper that adapts its width and elevation based on screen size
class ResponsiveCardWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;
  final EdgeInsets? margin;
  final EdgeInsets? padding;
  final double? elevation;

  const ResponsiveCardWrapper({
    super.key,
    required this.child,
    this.maxWidth = 1366.0,
    this.margin,
    this.padding,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLargeScreen = screenWidth > ResponsiveBreakpoints.tablet;

    final effectiveElevation = elevation ?? (isLargeScreen ? 8.0 : 4.0);
    final effectiveMargin = margin ?? const EdgeInsets.all(16.0);
    final effectivePadding = padding ?? const EdgeInsets.all(16.0);

    return ResponsiveLayoutWrapper(
      maxWidth: maxWidth,
      padding: effectiveMargin,
      child: Card(
        elevation: effectiveElevation,
        child: Padding(
          padding: effectivePadding,
          child: child,
        ),
      ),
    );
  }
}

/// Responsive breakpoints utility class
class ResponsiveBreakpoints {
  static const double mobile = 480.0;
  static const double tablet = 768.0;
  static const double desktop = 1024.0;
  static const double largeDesktop = 1440.0;

  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < tablet;
  }

  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= tablet && width < desktop;
  }

  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }

  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  static bool shouldUseBottomNavigation(BuildContext context) {
    // Use bottom navigation on mobile portrait or small screens
    return isMobile(context) && !isLandscape(context);
  }

  static bool shouldUseSideNavigation(BuildContext context) {
    // Use side navigation on tablets/desktop or landscape orientation
    return isLandscape(context);
  }
}
