import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/salon_subscription.dart';
import '../../providers/role_provider.dart';
import '../../providers/subscription_provider.dart';
import '../../screens/subscription/platform_subscription_screen.dart';

/// Widget that guards features based on subscription status
class SubscriptionGuard extends StatelessWidget {
  final String feature;
  final String salonId;
  final Widget child;
  final Widget? fallback;
  final String? upgradeMessage;
  final SubscriptionTier? requiredTier;

  const SubscriptionGuard({
    super.key,
    required this.feature,
    required this.salonId,
    required this.child,
    this.fallback,
    this.upgradeMessage,
    this.requiredTier,
  });

  @override
  Widget build(BuildContext context) {
    // Special handling for salon creation feature
    if (feature == 'salon_creation') {
      return Consumer2<SubscriptionProvider, RoleProvider>(
        builder: (context, subscriptionProvider, roleProvider, _) {
          // Only salon owners (chief groomers) can access subscription features
          if (!_isChiefGroomer(roleProvider, salonId)) {
            return _buildAccessDenied(context, 'Doar proprietarii de salon pot accesa această funcționalitate');
          }

          // Use FutureBuilder to check if user can create salons
          return FutureBuilder<bool>(
            future: subscriptionProvider.canUserCreateSalons(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              final canCreate = snapshot.data ?? false;
              if (canCreate) {
                return child;
              }

              // Show fallback or upgrade prompt
              return fallback ?? _buildUpgradePrompt(context, subscriptionProvider);
            },
          );
        },
      );
    }

    // Standard handling for other features
    return Consumer2<SubscriptionProvider, RoleProvider>(
      builder: (context, subscriptionProvider, roleProvider, _) {
        // Only salon owners (chief groomers) can access subscription features
        if (!_isChiefGroomer(roleProvider, salonId)) {
          return _buildAccessDenied(context, 'Doar proprietarii de salon pot accesa această funcționalitate');
        }

        // Check if subscription allows access to this feature
        if (_canAccessFeature(subscriptionProvider)) {
          return child;
        }

        // Show fallback or upgrade prompt
        return fallback ?? _buildUpgradePrompt(context, subscriptionProvider);
      },
    );
  }

  bool _isChiefGroomer(RoleProvider roleProvider, String salonId) {
    final permissions = roleProvider.permissions;
    if (permissions == null) return false;
    
    // Check if user has management access in this salon
    return permissions.hasManagementAccess;
  }

  bool _canAccessFeature(SubscriptionProvider subscriptionProvider) {
    // Special handling for salon creation - check user's highest tier across all salons
    if (feature == 'salon_creation') {
      // For salon creation, we need to check asynchronously
      // This will be handled in the build method with FutureBuilder
      return false; // Will be overridden by FutureBuilder
    }

    final subscription = subscriptionProvider.currentSubscription;
    if (subscription == null || !subscription.isActive) {
      return false;
    }

    // Check if current tier supports the feature
    return subscription.canAccessFeature(feature);
  }

  Widget _buildAccessDenied(BuildContext context, String message) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.lock,
            color: Colors.red.shade400,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            'Acces Restricționat',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.red.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.red.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradePrompt(BuildContext context, SubscriptionProvider subscriptionProvider) {
    final currentTier = subscriptionProvider.currentTier;
    final hasSubscription = subscriptionProvider.hasActiveSubscription;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade100,
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.upgrade,
            color: Colors.blue.shade600,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            hasSubscription ? 'Upgrade Necesar' : 'Abonament Necesar',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            upgradeMessage ?? _getDefaultUpgradeMessage(hasSubscription, currentTier),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.blue.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () => _navigateToSubscription(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
            child: Text(hasSubscription ? 'Upgrade Plan' : 'Vezi Planurile'),
          ),
        ],
      ),
    );
  }

  String _getDefaultUpgradeMessage(bool hasSubscription, SubscriptionTier? currentTier) {
    if (!hasSubscription) {
      return 'Pentru a accesa această funcționalitate, ai nevoie de un abonament activ.';
    }

    final requiredTierName = _getRequiredTierName();
    return 'Pentru a accesa această funcționalitate, ai nevoie de planul $requiredTierName sau superior.';
  }

  String _getRequiredTierName() {
    if (requiredTier != null) {
      return requiredTier!.name;
    }

    // Determine required tier based on feature
    switch (feature) {
      case 'advanced_reports':
      case 'inventory_management':
      case 'marketing_tools':
      case 'online_booking_widget':
        return 'Silver';
      case 'multi_location':
      case 'api_access':
      case 'custom_branding':
      case 'salon_creation':
        return 'Gold';
      default:
        return 'Bronze';
    }
  }

  void _navigateToSubscription(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PlatformSubscriptionScreen(
          salonId: salonId,
        ),
      ),
    );
  }
}

/// Simplified subscription guard for quick checks
class SimpleSubscriptionGuard extends StatelessWidget {
  final String salonId;
  final Widget child;
  final Widget? fallback;

  const SimpleSubscriptionGuard({
    super.key,
    required this.salonId,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer2<SubscriptionProvider, RoleProvider>(
      builder: (context, subscriptionProvider, roleProvider, _) {
        // Check if user is chief groomer and has active subscription
        final permissions = roleProvider.permissions;
        final hasManagementAccess = permissions?.hasManagementAccess ?? false;
        final hasActiveSubscription = subscriptionProvider.hasActiveSubscription;

        if (hasManagementAccess && hasActiveSubscription) {
          return child;
        }

        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}
