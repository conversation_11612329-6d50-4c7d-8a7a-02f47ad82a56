import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../models/salon_subscription.dart';
import '../../providers/subscription_provider.dart';
import '../../screens/subscription/platform_subscription_screen.dart';
import '../../services/auth/auth_service.dart';
import '../../services/subscription_limit_service.dart';

/// Widget that displays subscription status and quick usage overview
class SubscriptionStatusBar extends StatefulWidget {
  final String? salonId;
  final bool showUsageDetails;
  final bool compact;

  const SubscriptionStatusBar({
    super.key,
    this.salonId,
    this.showUsageDetails = true,
    this.compact = false,
  });

  @override
  State<SubscriptionStatusBar> createState() => _SubscriptionStatusBarState();
}

class _SubscriptionStatusBarState extends State<SubscriptionStatusBar> {
  SubscriptionLimits? _limits;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadLimits();

    // Listen to subscription provider changes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionProvider = Provider.of<SubscriptionProvider>(context, listen: false);
      subscriptionProvider.addListener(_onSubscriptionChanged);
    });
  }

  @override
  void dispose() {
    // Remove listener
    try {
      final subscriptionProvider = Provider.of<SubscriptionProvider>(context, listen: false);
      subscriptionProvider.removeListener(_onSubscriptionChanged);
    } catch (e) {
      // Context might be disposed
    }
    super.dispose();
  }

  void _onSubscriptionChanged() {
    if (mounted) {
      // Force refresh limits when subscription changes
      _refreshLimits();
    }
  }

  Future<void> _refreshLimits() async {
    try {
      final salonId = widget.salonId ?? await AuthService.getCurrentSalonId();
      if (salonId != null) {
        // Clear cache and reload
        SubscriptionLimitService.clearCache(salonId);
        await _loadLimits();
      }
    } catch (e) {
      debugPrint('⚠️ Failed to refresh subscription limits: $e');
    }
  }

  Future<void> _loadLimits() async {
    if (!widget.showUsageDetails) {
      setState(() => _isLoading = false);
      return;
    }

    try {
      final salonId = widget.salonId ?? await AuthService.getCurrentSalonId();
      if (salonId != null) {
        final limits = await SubscriptionLimitService.getLimits(salonId);
        if (mounted) {
          setState(() {
            _limits = limits;
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SubscriptionProvider>(
      builder: (context, subscriptionProvider, _) {
        if (widget.compact) {
          return _buildCompactView(subscriptionProvider);
        }
        return _buildFullView(subscriptionProvider);
      },
    );
  }

  Widget _buildCompactView(SubscriptionProvider subscriptionProvider) {
    final tier = subscriptionProvider.currentTier;
    final tierColor = _getTierColor(tier);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: tierColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: tierColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getTierIcon(tier),
            size: 16,
            color: tierColor,
          ),
          const SizedBox(width: 6),
          Text(
            _getTierDisplayName(tier),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: tierColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFullView(SubscriptionProvider subscriptionProvider) {
    final tier = subscriptionProvider.currentTier;
    final tierColor = _getTierColor(tier);
    final hasActiveSubscription = subscriptionProvider.hasActiveSubscription;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with tier and upgrade button
            Row(
              children: [
                Icon(
                  _getTierIcon(tier),
                  color: tierColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Plan ${_getTierDisplayName(tier)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: tierColor,
                        ),
                      ),
                      if (!hasActiveSubscription)
                        Text(
                          'Abonament inactiv',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _navigateToSubscription,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: tierColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    tier == SubscriptionTier.enterprise ? 'Gestionează' : 'Upgrade',
                  ),
                ),
              ],
            ),

            // Usage overview (if enabled and data available)
            if (widget.showUsageDetails && !_isLoading && _limits != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              _buildUsageOverview(),
            ],

            // Tier benefits
            const SizedBox(height: 16),
            _buildTierBenefits(tier),
          ],
        ),
      ),
    );
  }

  Widget _buildUsageOverview() {
    if (_limits == null) return const SizedBox.shrink();

    final hasWarnings = _limits!.isStaffLimitApproaching() ||
                       _limits!.isClientLimitApproaching() ||
                       _limits!.isSmsLimitApproaching() ||
                       !_limits!.canAddStaff() ||
                       !_limits!.canAddClients() ||
                       !_limits!.canSendSms();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              'Utilizare Curentă',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (hasWarnings) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.warning,
                size: 16,
                color: Colors.orange,
              ),
            ],
          ],
        ),
        const SizedBox(height: 12),
        
        // Usage items
        _buildUsageItem(
          'Angajați',
          _limits!.getStaffUsageText(),
          _limits!.getStaffUsagePercentage(),
          Icons.group,
        ),
        const SizedBox(height: 8),
        _buildUsageItem(
          'Animale',
          _limits!.getClientUsageText(),
          _limits!.getClientUsagePercentage(),
          Icons.pets,
        ),
        const SizedBox(height: 8),
        _buildUsageItem(
          'SMS',
          _limits!.getSmsUsageText(),
          _limits!.getSmsUsagePercentage(),
          Icons.sms,
        ),
      ],
    );
  }

  Widget _buildUsageItem(String label, String usage, double percentage, IconData icon) {
    final color = _getUsageColor(percentage);
    
    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            '$label: $usage',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        Container(
          width: 60,
          height: 6,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: Colors.grey[300],
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: (percentage / 100).clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(3),
                color: color,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTierBenefits(SubscriptionTier? tier) {
    if (tier == null) {
      return const SizedBox.shrink();
    }
    final benefits = _getTierBenefits(tier);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Beneficii Plan',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        ...benefits.map((benefit) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                size: 16,
                color: _getTierColor(tier),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  benefit,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Color _getTierColor(SubscriptionTier? tier) {
    if (tier == null) return Colors.grey;

    switch (tier) {
      case SubscriptionTier.free:
        return Colors.grey[400]!;
      case SubscriptionTier.freelancer:
        return Colors.brown;
      case SubscriptionTier.team:
        return Colors.grey[600]!;
      case SubscriptionTier.enterprise:
        return Colors.amber[700]!;
    }
  }

  IconData _getTierIcon(SubscriptionTier? tier) {
    if (tier == null) return Icons.help_outline;

    switch (tier) {
      case SubscriptionTier.free:
        return Icons.star_outline;
      case SubscriptionTier.freelancer:
        return Icons.workspace_premium;
      case SubscriptionTier.team:
        return Icons.star;
      case SubscriptionTier.enterprise:
        return Icons.diamond;
    }
  }

  String _getTierDisplayName(SubscriptionTier? tier) {
    if (tier == null) return 'Necunoscut';

    switch (tier) {
      case SubscriptionTier.free:
        return 'Gratuit';
      case SubscriptionTier.freelancer:
        return 'Bronze';
      case SubscriptionTier.team:
        return 'Silver';
      case SubscriptionTier.enterprise:
        return 'Gold';
    }
  }

  List<String> _getTierBenefits(SubscriptionTier tier) {
    switch (tier) {
      case SubscriptionTier.free:
        return [
          '1 groomer',
          'Programări de bază',
          'Fișe client simple',
          'Fără SMS',
        ];
      case SubscriptionTier.freelancer:
        return [
          '2 angajați',
          '500 animale',
          '50 SMS/lună',
          'Programări de bază',
        ];
      case SubscriptionTier.team:
        return [
          '5 angajați',
          'Animale nelimitate',
          '200 SMS/lună',
          'Rapoarte avansate',
          'Gestionare inventar',
        ];
      case SubscriptionTier.enterprise:
        return [
          'Angajați nelimitați',
          'Animale nelimitate',
          '500 SMS/lună',
          'Multi-locații',
          'Acces API',
          'Branding personalizat',
        ];
    }
  }

  Color _getUsageColor(double percentage) {
    if (percentage >= 100) return Colors.red;
    if (percentage >= 80) return Colors.orange;
    if (percentage >= 60) return Colors.yellow[700]!;
    return Colors.green;
  }

  void _navigateToSubscription() async {
    final salonId = widget.salonId ?? await AuthService.getCurrentSalonId();
    if (salonId != null && mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => PlatformSubscriptionScreen(
            salonId: salonId,
          ),
        ),
      );
    }
  }
}
