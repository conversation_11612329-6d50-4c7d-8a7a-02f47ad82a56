import 'package:animaliaproject/utils/debug_logger.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../services/staff_service.dart';
import '../../services/tour_keys.dart';
import '../common/custom_bottom_sheet.dart';
import '../dialogs/appointment_details_dialog.dart';
import '../dialogs/block_time_details_sheet.dart';
import 'day_view.dart';
import 'month_view.dart';
import 'week_view.dart';

enum CalendarViewMode { day, week, month }

class GoogleCalendarView extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final DateTime selectedDate;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final ValueChanged<CalendarViewMode>? onViewModeChange;
  final ValueChanged<DateTime>? onDateChanged;
  final VoidCallback? onRefreshPressed;
  final VoidCallback? onSettingsPressed;

  const GoogleCalendarView({
    Key? key,
    required this.currentViewMode,
    required this.selectedDate,
    this.onTimeSlotTap,
    this.onViewModeChange,
    this.onDateChanged,
    this.onRefreshPressed,
    this.onSettingsPressed,
  }) : super(key: key);

  @override
  State<GoogleCalendarView> createState() => _GoogleCalendarViewState();
}

enum _SwipeDirection { next, previous }

class _GoogleCalendarViewState extends State<GoogleCalendarView> {
  late DateTime _selectedDate;
  _SwipeDirection _swipeDirection = _SwipeDirection.next;
  DateTime? _lastNavigationTime; // Prevent rapid navigation

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  @override
  void didUpdateWidget(GoogleCalendarView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentViewMode != widget.currentViewMode ||
        oldWidget.selectedDate != widget.selectedDate) {
      if (oldWidget.selectedDate != widget.selectedDate) {
        _selectedDate = widget.selectedDate;
      }
      // Defer data loading to avoid build-time state updates
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadDataForCurrentView();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _loadDataForCurrentView() {
    final provider = Provider.of<CalendarProvider>(context, listen: false);

    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        provider.fetchAppointmentsForDate(_selectedDate);
        provider.fetchBlockedTimesForDate(_selectedDate);
        break;
      case CalendarViewMode.week:
        _loadWeekData(provider);
        break;
      case CalendarViewMode.month:
        _loadMonthData(provider);
        break;
    }
  }

  void _loadWeekData(CalendarProvider provider) {
    final weekStart = _getWeekStart(_selectedDate);
    DebugLogger.logVerbose('📅 Loading week data with optimized single API calls for week starting: ${weekStart.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire week
    provider.fetchAppointmentsForWeek(weekStart);
    provider.fetchBlockedTimesForWeek(weekStart);
  }

  void _loadMonthData(CalendarProvider provider) {
    final monthStart = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final monthEnd = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);
    DebugLogger.logVerbose('📅 Loading month data with optimized single API calls for month: ${monthStart.toString().split(' ')[0]} to ${monthEnd.toString().split(' ')[0]}');

    // Use optimized methods that make single API calls for the entire month
    provider.fetchAppointmentsForDateRange(monthStart, monthEnd);
    provider.fetchBlockedTimesForDateRange(monthStart, monthEnd);
  }

  DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Professional Apple Calendar-style header
        _buildHeader(),
        Expanded(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200), // Reduced from 500ms to eliminate white flash
            switchInCurve: Curves.easeOutCubic,
            switchOutCurve: Curves.easeOutCubic,
            transitionBuilder: (child, animation) {
              final beginOffset = _swipeDirection == _SwipeDirection.next
                  ? const Offset(1, 0)
                  : const Offset(-1, 0);
              final curvedAnimation =
                  CurvedAnimation(parent: animation, curve: Curves.easeOutCubic);
              return SlideTransition(
                position: Tween<Offset>(begin: beginOffset, end: Offset.zero)
                    .animate(curvedAnimation),
                child: child,
              );
            },
            child: Container(
              color: Theme.of(context).colorScheme.background, // Use theme-aware background
              child: _buildCalendarContent(
                key: ValueKey(
                    '${widget.currentViewMode}-${_selectedDate.toIso8601String()}'),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildViewModeDropdown(BuildContext context) {
    // Map view modes to their labels
    const viewModeLabels = {
      CalendarViewMode.day: 'Zi',
      CalendarViewMode.week: 'Săpt',
      CalendarViewMode.month: 'Lună',
    };

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline,
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<CalendarViewMode>(
          value: widget.currentViewMode,
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: Theme.of(context).colorScheme.onSurface,
            size: 16,
          ),
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          dropdownColor: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          items: CalendarViewMode.values.map((CalendarViewMode mode) {
            return DropdownMenuItem<CalendarViewMode>(
              value: mode,
              child: Text(
                viewModeLabels[mode]!,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 12,
                  fontWeight: mode == widget.currentViewMode
                      ? FontWeight.bold
                      : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
          onChanged: (CalendarViewMode? newMode) {
            if (newMode != null) {
              widget.onViewModeChange?.call(newMode);
            }
          },
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {

        return Container(
          height: 56, // Professional Apple Calendar-style header height (increased for larger icons)
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Professional padding
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
                offset: const Offset(0, 1),
                blurRadius: 3,
              ),
            ],
          ),
          child: Row(
            children: [
              // Left side - View mode dropdown
              Showcase(
                key: CalendarTourKeys.viewToggleKey,
                title: 'Vizualizare',
                description: 'Comută între vedere pe zi, săptămână sau lună.',
                child: _buildViewModeDropdown(context),
              ),

              // Week/Day navigation with range text between arrows
              Expanded(
                child: Row(
                  children: [
                    IconButton(
                      onPressed: _goToPrevious,
                      icon: Icon(Icons.chevron_left,
                          color: Theme.of(context).colorScheme.onSurface,
                          size: 30),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                    ),
                    Expanded(
                      child: Showcase(
                        key: CalendarTourKeys.todayButtonKey,
                        title: 'Navigație',
                        description: 'Apasă pe dată pentru a merge la ziua de astăzi.',
                        child: GestureDetector(
                          onTap: _goToToday,
                          child: Text(
                            _getNavigationRangeText(),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _goToNext,
                      icon: Icon(Icons.chevron_right,
                          color: Theme.of(context).colorScheme.onSurface,
                          size: 30),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
                    ),
                  ],
                ),
              ),

              IconButton(
                onPressed: _addSlotForStaff(staffMember),
                icon: Icon(Icons.add,
                    color: Theme.of(context).colorScheme.onSurface,
                    size: 30),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(minWidth: 38, minHeight: 38), // Larger sizing for bigger icons
              ),


              // Right side - Empty (buttons are now in AppBar)
              const SizedBox.shrink(),
            ],
          ),
        );
      },
    );
  }

  Future<void> _addSlotForStaff(StaffResponse originalStaff) async {
    try {
      final provider = context.read<CalendarProvider>();
      final currentSlotCount = _getCurrentSlotCount(originalStaff.id);
      final newSlotNumber = currentSlotCount + 2; // Start from 2 (original is slot 1)

      // Create a new staff member as a slot
      final slotNotes = 'SLOT_FOR:${originalStaff.id}|SLOT_NUMBER:$newSlotNumber';
      final slotNickname = '${originalStaff.displayName} - Slot $newSlotNumber';

      // Call the staff creation service
      await _createSlotStaff(slotNickname, originalStaff.groomerRole.value, slotNotes);

      // Refresh calendar data
      await provider.refreshStaffData();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Slot adăugat pentru ${originalStaff.displayName}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare la adăugarea slot-ului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _createSlotStaff(String nickname, String role, String notes) async {
    // Convert role string to GroomerRole enum
    GroomerRole groomerRole;
    switch (role.toLowerCase()) {
      case 'chief_groomer':
        groomerRole = GroomerRole.chiefGroomer;
        break;
      case 'assistant':
        groomerRole = GroomerRole.assistant;
        break;
      default:
        groomerRole = GroomerRole.groomer;
    }

    // Create the request
    final request = CreateStaffDirectlyRequest.fromInput(
      nickname: nickname,
      groomerRole: groomerRole,
      notes: notes,
    );

    // Call the staff service
    final response = await StaffService.createStaffDirectlyInCurrentSalon(request);

    if (!response.success) {
      throw Exception(response.error ?? 'Failed to create slot staff');
    }
  }

  Widget _buildCalendarContent({Key? key}) {
    return KeyedSubtree(
      key: key,
      child: Consumer<CalendarProvider>(
        builder: (context, provider, child) {
          // Show empty state when:
          // 1. No groomers are selected, OR
          // 2. Staff is still loading, OR
          // 3. There was an error loading staff, OR
          // 4. Available staff is empty but no error (edge case)
          if (provider.selectedStaff.isEmpty ||
              provider.isLoadingStaff ||
              provider.staffError != null ||
              (provider.availableStaff.isEmpty && provider.staffError == null && !provider.isLoadingStaff)) {
            return _buildEmptyStaffState(provider);
          }

          switch (widget.currentViewMode) {
            case CalendarViewMode.day:
              return _buildDayView(provider);
            case CalendarViewMode.week:
              return _buildWeekView(provider);
            case CalendarViewMode.month:
              return _buildMonthView(provider);
          }
        },
      ),
    );
  }

  Widget _buildEmptyStaffState(CalendarProvider provider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                Icons.people_outline,
                size: 40,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              provider.availableStaff.isEmpty
                  ? 'Niciun groomer în echipă'
                  : 'Niciun groomer selectat',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),

            // Description
            Text(
              provider.availableStaff.isEmpty
                  ? 'Nu există groomeri în echipă. Adaugă membri în echipă pentru a vedea programările și orele de lucru.'
                  : 'Pentru a vedea programările și orele de lucru, bifează cel puțin un membru al personalului din meniul de setări.',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),

            // Action buttons
            Column(
              children: [
                if (provider.availableStaff.isNotEmpty) ...[
                  // Primary button - Select staff (when staff available)
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Open the drawer
                        widget.onSettingsPressed?.call();
                      },
                      icon: const Icon(Icons.people),
                      label: const Text('Bifează personalul'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Secondary button - Select all
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        provider.selectAllStaff();
                      },
                      icon: const Icon(Icons.done_all),
                      label: Text('Bifează tot personalul (${provider.availableStaff.length})'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        side: BorderSide(color: Theme.of(context).colorScheme.primary),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ] else ...[
                  // Button for when no staff available
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Open the drawer to show team management options
                        widget.onSettingsPressed?.call();
                      },
                      icon: const Icon(Icons.group_add),
                      label: const Text('Gestionează echipa'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDayView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();
    DebugLogger.logVerbose('📅 DayView business hours: openTime=${businessHours['openTime']}, closeTime=${businessHours['closeTime']}');

    return DayView(
      key: DayView.globalKey,
      selectedDate: _selectedDate,
      appointments: provider.getFilteredAppointmentsForDate(_selectedDate),
      onAppointmentTap: _showAppointmentDetails,
      onBlockTap: _showBlockTimeDetails,
      onTimeSlotTap: widget.onTimeSlotTap,
      businessHours: businessHours,
    );
  }

  Widget _buildWeekView(CalendarProvider provider) {
    // Get fresh business hours each time to ensure updates are reflected
    final businessHours = provider.getBusinessHours();
    DebugLogger.logVerbose('📅 WeekView business hours: openTime=${businessHours['openTime']}, closeTime=${businessHours['closeTime']}');

    return WeekView(
      key: WeekView.globalKey,
      selectedWeek: _selectedDate,
      appointmentsByDay: _groupAppointmentsByDay(provider),
      onAppointmentTap: _showAppointmentDetails,
      onBlockTap: _showBlockTimeDetails,
      onTimeSlotTap: widget.onTimeSlotTap,
      onDayTap: _selectDay,
      businessHours: businessHours,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Widget _buildMonthView(CalendarProvider provider) {
    return MonthView(
      selectedMonth: _selectedDate,
      appointmentsByDay: _groupAppointmentsByMonth(provider),
      onAppointmentTap: _showAppointmentDetails,
      onDayTap: _selectDay,
      onSwipeToNext: () => _changeDate(next: true),
      onSwipeToPrevious: () => _changeDate(next: false),
    );
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByDay(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For week view, get filtered appointments for each day from the cache
    final weekStart = _getWeekStart(_selectedDate);
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dayKey = DateTime(day.year, day.month, day.day);

      // Get filtered appointments for this specific day from cache
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(day);
    }

    return grouped;
  }

  Map<DateTime, List<Appointment>> _groupAppointmentsByMonth(
      CalendarProvider provider) {
    final Map<DateTime, List<Appointment>> grouped = {};

    // For month view, get appointments for the entire month
    final firstDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month, 1);
    final lastDayOfMonth = DateTime(_selectedDate.year, _selectedDate.month + 1, 0);

    // Include days from previous/next month that appear in the calendar grid
    final firstDayToShow = firstDayOfMonth.subtract(Duration(days: (firstDayOfMonth.weekday - 1) % 7));
    final lastDayToShow = firstDayToShow.add(const Duration(days: 41)); // 6 weeks = 42 days

    // Load appointments for each day in the visible range
    DateTime currentDay = firstDayToShow;
    while (currentDay.isBefore(lastDayToShow) || currentDay.isAtSameMomentAs(lastDayToShow)) {
      final dayKey = DateTime(currentDay.year, currentDay.month, currentDay.day);
      grouped[dayKey] = provider.getFilteredAppointmentsForDate(currentDay);
      currentDay = currentDay.add(const Duration(days: 1));
    }

    return grouped;
  }

  void _showAppointmentDetails(Appointment appointment) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AppointmentDetailsDialog(appointment: appointment);
      },
    );
  }

  void _showBlockTimeDetails(Map<String, dynamic> block) {
    CustomBottomSheet.show(
      context: context,
      title: 'Detalii blocare',
      isScrollControlled: true,
      child: BlockTimeDetailsSheet(
        block: block,
        isWeekView: widget.currentViewMode == CalendarViewMode.week,
      ),
    );
  }

  void _selectDay(DateTime day) {
    // Switch to day view when a day is selected from week view
    if (widget.currentViewMode != CalendarViewMode.day) {
      widget.onViewModeChange?.call(CalendarViewMode.day);
    }

    setState(() {
      _selectedDate = day;
    });

    widget.onDateChanged?.call(_selectedDate);

    // Data for the new day will be loaded when the view mode updates
  }

  void _changeDate({required bool next}) {
    // Prevent rapid navigation
    final now = DateTime.now();
    if (_lastNavigationTime != null && 
        now.difference(_lastNavigationTime!).inMilliseconds < 150) {
      return;
    }
    _lastNavigationTime = now;
    
    final maxDate = DateTime.now().add(const Duration(days: 90));
    DateTime newDate;
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        newDate = next
            ? _selectedDate.add(const Duration(days: 1))
            : _selectedDate.subtract(const Duration(days: 1));
        break;
      case CalendarViewMode.week:
        newDate = next
            ? _selectedDate.add(const Duration(days: 7))
            : _selectedDate.subtract(const Duration(days: 7));
        break;
      case CalendarViewMode.month:
        newDate = DateTime(
          _selectedDate.year,
          _selectedDate.month + (next ? 1 : -1),
          1,
        );
        break;
    }

    if (next && newDate.isAfter(maxDate)) {
      return;
    }

    setState(() {
      _swipeDirection = next ? _SwipeDirection.next : _SwipeDirection.previous;
      _selectedDate = newDate;
    });

    widget.onDateChanged?.call(_selectedDate);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
    });
  }

  void _goToPrevious() {
    _changeDate(next: false);
  }

  void _goToNext() {
    _changeDate(next: true);
  }

  void _goToToday() {
    final today = DateTime.now();
    setState(() {
      _swipeDirection = today.isAfter(_selectedDate)
          ? _SwipeDirection.next
          : _SwipeDirection.previous;
      _selectedDate = today;
    });

    widget.onDateChanged?.call(_selectedDate);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDataForCurrentView();
      // Call goToToday which will handle both navigation and scrolling
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.currentViewMode == CalendarViewMode.day) {
          DayView.globalKey.currentState?.goToToday();
        } else if (widget.currentViewMode == CalendarViewMode.week) {
          WeekView.globalKey.currentState?.goToToday();
        }
      });
    });
  }

  // Helper methods for header
  String _getNavigationRangeText() {
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('EEE dd', 'ro').format(_selectedDate).toUpperCase();
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        final weekEnd = weekStart.add(const Duration(days: 6));
        final startText = DateFormat('EEE d', 'ro').format(weekStart).toUpperCase();
        final endText = DateFormat('EEE d', 'ro').format(weekEnd).toUpperCase();
        return '$startText - $endText';
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
    }
  }

  // Method to get month/year text for AppBar
  String getMonthYearText() {
    switch (widget.currentViewMode) {
      case CalendarViewMode.day:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
      case CalendarViewMode.week:
        final weekStart = _getWeekStart(_selectedDate);
        return DateFormat('MMMM yyyy', 'ro').format(weekStart);
      case CalendarViewMode.month:
        return DateFormat('MMMM yyyy', 'ro').format(_selectedDate);
    }
  }


}
