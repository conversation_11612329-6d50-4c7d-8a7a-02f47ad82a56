import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/staff_service.dart';
import '../../models/user_role.dart';
import '../dialogs/block_time_dialog.dart';
import 'block_time_block.dart';
import 'draggable_appointment_block.dart';
import 'droppable_time_slot.dart';
import 'time_slot.dart';

class WeekView extends StatefulWidget {
  /// Global key to access state for scrolling from outside the widget tree
  static final GlobalKey<_WeekViewState> globalKey = GlobalKey<_WeekViewState>();
  final DateTime selectedWeek;
  final Map<DateTime, List<Appointment>> appointmentsByDay;
  final Function(Appointment) onAppointmentTap;
  final Function(Map<String, dynamic>)? onBlockTap;
  final Function(DateTime, String?)? onTimeSlotTap; // Added staff ID parameter
  final Function(DateTime)? onDayTap;
  final Map<String, dynamic> businessHours;
  final VoidCallback? onSwipeToNext;
  final VoidCallback? onSwipeToPrevious;

  const WeekView({
    super.key,
    required this.selectedWeek,
    required this.appointmentsByDay,
    required this.onAppointmentTap,
    this.onBlockTap,
    this.onTimeSlotTap,
    this.onDayTap,
    required this.businessHours,
    this.onSwipeToNext,
    this.onSwipeToPrevious,
  });

  @override
  State<WeekView> createState() => _WeekViewState();
}

class _WeekViewState extends State<WeekView> {
  ScrollController? _horizontalScrollController;
  ScrollController? _verticalScrollController;
  ScrollController? _timeVerticalScrollController;
  bool _initialScrollDone = false;
  
  // Track loaded staff to prevent duplicate API calls
  Set<String> _loadedStaffIds = {};
  bool _isLoadingStaffHours = false;

  // Swipe gesture detection variables
  double? _swipeStartX;
  double? _swipeStartY;
  bool _isHorizontalScrolling = false;
  DateTime? _lastSwipeTime; // Prevent rapid swipes

  // Swipe thresholds - optimized
  static const double _minSwipeDistance = 60.0;
  static const double _minSwipeVelocity = 150.0;
  static const double _maxVerticalDeviation = 50.0;

  // Cached layout information for scrolling
  List<DateTime> _currentWeekDays = [];
  int _visibleStaffCount = 0;
  double _staffColumnWidth = 100.0;
  double? _slotHeight;
  int? _openTime;

  @override
  void initState() {
    super.initState();
    _horizontalScrollController = ScrollController();
    _verticalScrollController = ScrollController();
    _timeVerticalScrollController = ScrollController();

    // Synchronize the vertical scroll controllers
    _verticalScrollController?.addListener(_syncVerticalScroll);
    _timeVerticalScrollController?.addListener(_syncTimeScroll);

    // Listen to horizontal scroll to detect when user is scrolling staff columns
    _horizontalScrollController?.addListener(() {
      if (_horizontalScrollController?.position.isScrollingNotifier.value == true) {
        _isHorizontalScrolling = true;
        // Reset swipe tracking when scrolling starts
        _swipeStartX = null;
        _swipeStartY = null;
      } else {
        // Add a small delay before allowing swipes again after scrolling stops
        Future.delayed(const Duration(milliseconds: 100), () {
          _isHorizontalScrolling = false;
        });
      }
    });
  }

  void _syncVerticalScroll() {
    if (_timeVerticalScrollController != null &&
        _verticalScrollController != null &&
        _timeVerticalScrollController!.hasClients &&
        _verticalScrollController!.hasClients) {
      _timeVerticalScrollController!.jumpTo(_verticalScrollController!.offset);
    }
  }

  void _syncTimeScroll() {
    // Time scroll is disabled, so this method is not needed anymore
    // but kept for compatibility
  }

  @override
  void dispose() {
    _verticalScrollController?.removeListener(_syncVerticalScroll);
    _timeVerticalScrollController?.removeListener(_syncTimeScroll);
    _horizontalScrollController?.dispose();
    _verticalScrollController?.dispose();
    _timeVerticalScrollController?.dispose();
    super.dispose();
  }
  
  @override
  void didUpdateWidget(WeekView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Clear loaded staff when week changes to allow reloading
    if (oldWidget.selectedWeek != widget.selectedWeek) {
      _loadedStaffIds.clear();
      _isLoadingStaffHours = false;
    }
  }

  // Swipe gesture handlers
  void _onPanStart(DragStartDetails details) {
    // Only start tracking if not currently scrolling
    if (!_isHorizontalScrolling) {
      _swipeStartX = details.globalPosition.dx;
      _swipeStartY = details.globalPosition.dy;
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    // If horizontal scrolling is active, don't process swipe gestures
    if (_isHorizontalScrolling) return;

    // Check if this looks like a horizontal scroll rather than a swipe
    if (_swipeStartX != null && _swipeStartY != null) {
      final deltaX = (details.globalPosition.dx - _swipeStartX!).abs();
      final deltaY = (details.globalPosition.dy - _swipeStartY!).abs();

      // More restrictive vertical deviation check
      if (deltaY > _maxVerticalDeviation || deltaY > deltaX * 0.3) {
        _swipeStartX = null;
        _swipeStartY = null;
        return;
      }
      
      // If movement is too small, don't consider it a swipe yet
      if (deltaX < 15.0) {
        return;
      }
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (_swipeStartX == null || _swipeStartY == null || _isHorizontalScrolling) {
      _swipeStartX = null;
      _swipeStartY = null;
      return;
    }

    final velocity = details.velocity.pixelsPerSecond;
    final horizontalVelocity = velocity.dx.abs();
    final verticalVelocity = velocity.dy.abs();
    
    // Calculate actual swipe distance
    final swipeDistance = (_swipeStartX! - details.globalPosition.dx).abs();

    // Prevent rapid swipes
    final now = DateTime.now();
    if (_lastSwipeTime != null && 
        now.difference(_lastSwipeTime!).inMilliseconds < 600) {
      _swipeStartX = null;
      _swipeStartY = null;
      return;
    }

    // Balanced swipe detection:
    // 1. Must have sufficient velocity
    // 2. Must have sufficient distance
    // 3. Horizontal velocity must be higher than vertical
    // 4. Must not be actively scrolling horizontally
    if (horizontalVelocity > _minSwipeVelocity &&
        swipeDistance > _minSwipeDistance &&
        horizontalVelocity > verticalVelocity * 1.5 && // Easier ratio
        !_isHorizontalScrolling) {

      _lastSwipeTime = now; // Record the swipe time
      
      if (velocity.dx > 0) {
        // Swipe right - go to previous week
        widget.onSwipeToPrevious?.call();
      } else {
        // Swipe left - go to next week
        widget.onSwipeToNext?.call();
      }
    }

    _swipeStartX = null;
    _swipeStartY = null;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        // Determine time range based on hour view mode
        final businessOpenTime = widget.businessHours['openTime'] as int;
        final businessCloseTime = widget.businessHours['closeTime'] as int;

        final openTime = provider.showFullDay ? 0 : businessOpenTime;
        final closeTime = provider.showFullDay ? 24 : businessCloseTime;

        // Adjust totalHours to exclude the last hour (end time)
        final totalHours = closeTime - openTime;

        final lunchStart = widget.businessHours['lunchBreak']['start'] as int;
        final lunchEnd = widget.businessHours['lunchBreak']['end'] as int;
        final staff = provider.availableStaff;
        final visibleStaff =
        staff.where((s) => provider.selectedStaff.contains(s.id)).toList();

        // Load staff working hours on demand for visible staff (optimized batch approach)
        // Only load if we haven't already loaded for these staff members
        final staffIds = visibleStaff.map((s) => s.id).toList();
        final staffIdsSet = staffIds.toSet();
        
        if (staffIds.isNotEmpty && 
            !_isLoadingStaffHours && 
            !staffIdsSet.every((id) => _loadedStaffIds.contains(id))) {
          
          _isLoadingStaffHours = true;
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            try {
              await provider.loadStaffWorkingHoursOnDemand(staffIds, reason: 'Week view visible staff');
              if (mounted) {
                setState(() {
                  _loadedStaffIds.addAll(staffIds);
                });
              }
            } finally {
              if (mounted) {
                _isLoadingStaffHours = false;
              }
            }
          });
        }

        final slotHeight = provider.getResponsiveTimeSlotHeight(context); // Use responsive height based on screen size
        final staffColumnWidth = provider.getResponsiveStaffColumnWidth(context); // Use responsive width based on screen size

        // Get the week days (Monday to Sunday)
        final weekDays = _getWeekDays(widget.selectedWeek);

        // Cache layout info for auto-scrolling
        _currentWeekDays = weekDays;
        _visibleStaffCount = visibleStaff.length;
        _staffColumnWidth = staffColumnWidth;
        _slotHeight = slotHeight;
        _openTime = openTime;

        // Auto-scroll to today's column on first build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (!_initialScrollDone) {
            _scrollToToday(weekDays, visibleStaff.length, staffColumnWidth);
            _initialScrollDone = true;
          }
        });

        return Container(
          color: Theme.of(context).colorScheme.background, // Use theme-aware background
          child: Column(
            children: [
              // Combined header and calendar with synchronized scrolling
              Expanded(
                child: GestureDetector(
                  onPanStart: _onPanStart,
                  onPanUpdate: _onPanUpdate,
                  onPanEnd: _onPanEnd,
                  behavior: HitTestBehavior.translucent, // Improve gesture detection
                  child: Row(
                    children: [
                      // Fixed time labels column
                      Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Container(
                            width: 60,
                            color: Theme.of(context).colorScheme.surfaceVariant,
                            child: Column(
                              children: [
                                _buildWeekTimeHeader(),
                                Expanded(
                                  child: SingleChildScrollView(
                                    controller: _timeVerticalScrollController,
                                    physics: const NeverScrollableScrollPhysics(),
                                    child: Column(
                                      children:
                                      List.generate(totalHours, (index) {
                                        final hour = openTime + index;
                                        final time = DateTime(2024, 1, 1, hour);
                                        final isCurrentHour =
                                            DateTime.now().hour == hour;

                                        return TimeLabel(
                                          time: time,
                                          isCurrentHour: isCurrentHour,
                                          height: slotHeight, // Use dynamic height
                                        );
                                      }),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                      // Scrollable calendar content
                      Expanded(
                        child: NotificationListener<OverscrollNotification>(
                          onNotification: (notification) {
                            // Overscroll navigation with debouncing
                            final now = DateTime.now();
                            if (_lastSwipeTime != null && 
                                now.difference(_lastSwipeTime!).inMilliseconds < 600) {
                              return true;
                            }
                            
                            if (_horizontalScrollController != null) {
                              if (notification.overscroll > 20.0 &&
                                  _horizontalScrollController!.position.pixels >=
                                      _horizontalScrollController!
                                          .position.maxScrollExtent) {
                                _lastSwipeTime = now;
                                widget.onSwipeToNext?.call();
                              } else if (notification.overscroll < -20.0 &&
                                  _horizontalScrollController!.position.pixels <=
                                      _horizontalScrollController!
                                          .position.minScrollExtent) {
                                _lastSwipeTime = now;
                                widget.onSwipeToPrevious?.call();
                              }
                            }
                            return false;
                          },
                          child: NotificationListener<ScrollNotification>(
                            onNotification: (notification) {
                              // Prevent accidental week navigation from scroll wheel
                              if (notification is ScrollUpdateNotification) {
                                final scrollDelta = notification.scrollDelta ?? 0;
                                // If scroll is too fast, it might be accidental
                                if (scrollDelta.abs() > 100) {
                                  return true; // Consume the notification
                                }
                              }
                              return false;
                            },
                            child: SingleChildScrollView(
                              controller: _horizontalScrollController,
                              scrollDirection: Axis.horizontal,
                              physics: const ClampingScrollPhysics(), // Reduce bounce effect
                            child: SizedBox(
                              width: weekDays.length *
                                  visibleStaff.length *
                                  staffColumnWidth,
                              child: Column(
                                children: [
                                  _buildSynchronizedWeekHeader(
                                      weekDays, visibleStaff, staffColumnWidth),
                                  Expanded(
                                    child: SingleChildScrollView(
                                      controller: _verticalScrollController,
                                      child: Row(
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        children: [
                                          ...weekDays.map((day) {
                                            final isWorkDay =
                                            provider.isWorkingDay(day);
                                            final dayAppointments =
                                                widget.appointmentsByDay[
                                                _dateKey(day)] ??
                                                    [];

                                            return Row(
                                              children: visibleStaff
                                                  .map((staffMember) =>
                                                  _buildDayStaffColumn(
                                                    provider,
                                                    day,
                                                    staffMember,
                                                    dayAppointments,
                                                    staffColumnWidth,
                                                    slotHeight,
                                                    totalHours,
                                                    openTime,
                                                    closeTime,
                                                    lunchStart,
                                                    lunchEnd,
                                                    isWorkDay,
                                                  ))
                                                  .toList(),
                                            );
                                          }),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSynchronizedWeekHeader(
      List<DateTime> weekDays,
      List<StaffResponse> visibleStaff,
      double staffColumnWidth) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Row(
        children: [
          // Day-Staff headers
          ...weekDays.map((day) {
            final isToday = _isToday(day);
            final provider = context.read<CalendarProvider>();
            final isWorkDay = provider.isWorkingDay(day);

            return GestureDetector(
              onTap: widget.onDayTap != null ? () => widget.onDayTap!(day) : null,
              child: Row(
                children: visibleStaff
                    .map((staffMember) => SizedBox(
                  width: staffColumnWidth,
                  height: 40,
                  child: Container(
                    decoration: BoxDecoration(
                      color: isToday
                          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                          : Colors.transparent,
                      // Remove vertical lines between staff columns
                      border: Border(
                        // Color underline to identify staff
                        bottom: BorderSide(
                          color: AppTheme.getStaffColor(staffMember.id),
                          width: 3,
                        ),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Day text
                        Text(
                          DateFormat('EEE d', 'ro').format(day),
                          style: TextStyle(
                            color: isToday
                                ? Theme.of(context).colorScheme.primary
                                : (isDark ? AppColors.darkText : AppColors.lightText),
                            fontSize: 12,
                            fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        // Staff name with add slot button
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: Text(
                                _getStaffDisplayName(staffMember),
                                style: TextStyle(
                                  color: AppTheme.getStaffColor(staffMember.id),
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ))
                    .toList(),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildWeekTimeHeader() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: 60,
      height: 40,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
            width: 0.5,
          ),
        ),
      ),
      child: const SizedBox.shrink(), // No text, just spacing
    );
  }

  void _showQuickActionMenu(DateTime slotTime, String staffId) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading:  Icon(Icons.add),
              title: Text('Programare nouă'),
              onTap: () {
                Navigator.pop(context);
                widget.onTimeSlotTap?.call(slotTime, staffId);
              },
            ),
            ListTile(
              leading:  Icon(Icons.block),
              title: Text('Blochează timp'),
              onTap: () {
                Navigator.pop(context);
                showDialog(
                  context: context,
                  builder: (_) => BlockTimeDialog(
                    selectedDate: slotTime,
                    preselectedStaffId: staffId,
                    preselectedStartTime: TimeOfDay.fromDateTime(slotTime),
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildDayStaffColumn(
      CalendarProvider provider,
      DateTime day,
      StaffResponse staffMember,
      List<Appointment> dayAppointments,
      double columnWidth,
      double slotHeight,
      int totalHours,
      int openTime,
      int closeTime,
      int lunchStart,
      int lunchEnd,
      bool isWorkDay,
      ) {
    // Use synchronous method for immediate visual feedback
    // Note: staffWorking and dateClosureInfo are now handled in getTimeSlotStyling method

    final staffAppointments = dayAppointments.where((appointment) {
      if (appointment.groomerId != null &&
          appointment.groomerId!.isNotEmpty) {
        return appointment.groomerId == staffMember.id;
      }

      String appointmentStaffName;
      try {
        final groomer = appointment.assignedGroomer;
        appointmentStaffName =
        groomer.isNotEmpty ? groomer : 'Ana Popescu';
      } catch (e) {
        appointmentStaffName = 'Ana Popescu';
      }

      return appointmentStaffName == staffMember.name ||
          appointmentStaffName == staffMember.displayName;
    });

    final dayBlocks = provider.getBlockedTimesForDate(day);
    final staffBlocks = dayBlocks.where((block) {
      final ids = (block['staffIds'] as List).cast<String>();
      return ids.contains(staffMember.id);
    });

    return SizedBox(
      width: columnWidth,
      child: Stack(
        children: [
          Column(
            children: List.generate(totalHours, (index) {
              final hour = openTime + index;
              final slotTime = DateTime(
                day.year,
                day.month,
                day.day,
                hour,
              );

              final isPastSlot = slotTime.isBefore(DateTime.now());

              // Get comprehensive styling information
              final slotStyling = provider.getTimeSlotStyling(slotTime, staffMember.id);

              final businessOpenTime = widget.businessHours['openTime'] as int;
              final businessCloseTime = widget.businessHours['closeTime'] as int;

              // Special handling for the last hour - always grey it out
              final isLastHour = hour == closeTime - 1;
              if (isLastHour) {
                return DroppableTimeSlot(
                  dateTime: slotTime,
                  staffId: staffMember.id,
                  isBusinessHour: false,
                  isLunchBreak: false,
                  isAvailable: false,
                  isGreyedOut: true,
                  isPastSlot: isPastSlot,
                  height: slotHeight,
                  isDragEnabled: false,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                  ),
                );
              }

              // Determine if this is a business hour (considering salon closure and staff availability)
              final isBusinessHour = provider.showFullDay
                  ? (slotStyling.isAvailable && hour >= businessOpenTime && hour < businessCloseTime)
                  : (slotStyling.isAvailable && hour >= openTime && hour < closeTime);

              // Debug visual styling application
              if (slotStyling.isGreyedOut) {
                // todo optimize this debugPrint('🎨 Applying grey styling to week slot: ${slotTime.toIso8601String()} - ${slotStyling.disabledReason}');
              }

              final isLunchBreak = hour >= lunchStart && hour < lunchEnd;
              final hasAppointment = staffAppointments.any((apt) =>
              apt.startTime.hour <= hour && apt.endTime.hour > hour);

              return Container(
                decoration: BoxDecoration(
                  // Remove extra borders - let TimeSlot handle its own borders
                  color: slotStyling.isGreyedOut
                      ? Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.1)
                      : null,
                ),
                child: DroppableTimeSlot(
                  dateTime: slotTime,
                  staffId: staffMember.id,
                  isBusinessHour: isBusinessHour && !slotStyling.isGreyedOut,
                  isLunchBreak: isLunchBreak,
                  isAvailable: !hasAppointment && slotStyling.isAvailable,
                  isGreyedOut: slotStyling.isGreyedOut,
                  isPastSlot: isPastSlot,
                  height: slotHeight,
                  onTap: slotStyling.isInteractive
                      ? () => widget.onTimeSlotTap?.call(slotTime, staffMember.id)
                      : null,
                  onLongPress: slotStyling.isInteractive
                      ? () => _showQuickActionMenu(slotTime, staffMember.id)
                      : null,
                  isDragEnabled: true,
                  // Pass disabled reason to TimeSlot for internal handling
                  child: slotStyling.disabledReason != null
                      ? Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                    ),
                  )
                      : null,
                ),
              );
            }),
          ),
          ...staffBlocks.map((block) {
            final start = DateTime.parse(block['startTime']).toLocal();
            final end = DateTime.parse(block['endTime']).toLocal();

            final topOffset = ((start.hour - openTime) * slotHeight) +
                (start.minute / 60 * slotHeight);
            final duration = end.difference(start);
            final blockHeight = (duration.inMinutes / 60) * slotHeight;

            return Positioned(
              top: topOffset,
              left: 1,
              right: 1,
              child: BlockTimeBlock(
                block: block,
                height: blockHeight,
                onTap: () => widget.onBlockTap?.call(block),
              ),
            );
          }),
          ...staffAppointments.map((appointment) {
            final startHour = appointment.startTime.hour;
            final startMinute = appointment.startTime.minute;

            final topOffset = ((startHour - openTime) * slotHeight) +
                (startMinute / 60 * slotHeight);
            final duration =
            appointment.endTime.difference(appointment.startTime);
            final blockHeight = (duration.inMinutes / 60) * slotHeight;

            return Positioned(
              top: topOffset,
              left: 1,
              right: 1,
              child: ClipRect( // Add ClipRect to prevent visual overflow
                child: DraggableAppointmentBlock(
                  appointment: appointment,
                  height: blockHeight,
                  isCompact: true,
                  onTap: () => widget.onAppointmentTap(appointment),
                  isDragEnabled: true,
                ),
              ),
            );
          }),
          if (_isToday(day))
            _buildCurrentTimeIndicator(openTime, slotHeight),
        ],
      ),
    );
  }

  List<DateTime> _getWeekDays(DateTime week) {
    // Find Monday of the week
    final monday = week.subtract(Duration(days: week.weekday - 1));
    return List.generate(7, (index) => monday.add(Duration(days: index)));
  }

  DateTime _dateKey(DateTime date) {
    return DateTime(date.year, date.month, date.day);
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  void _scrollToToday(List<DateTime> weekDays, int staffCount, double columnWidth) {
    if (_horizontalScrollController == null) return;
    final index = weekDays.indexWhere(_isToday);
    if (index != -1 && _horizontalScrollController!.hasClients) {
      final offset = index * staffCount * columnWidth;
      final max = _horizontalScrollController!.position.maxScrollExtent;
      _horizontalScrollController!.jumpTo(offset.clamp(0.0, max));
    }
  }

  /// Scroll to a specific date and time in the week view
  // void scrollToDateTime(DateTime dateTime) {
  //   if (_verticalScrollController == null || !_verticalScrollController!.hasClients) return;
  //
  //   final businessHours = widget.businessHours;
  //   final openTime = businessHours['openTime'] as int? ?? 9;
  //   final closeTime = businessHours['closeTime'] as int? ?? 17;
  //
  //   // Clamp the hour to business hours
  //   final targetHour = dateTime.hour.clamp(openTime, closeTime - 1);
  //   final targetMinute = dateTime.minute;
  //
  //   final provider = Provider.of<CalendarProvider>(context, listen: false);
  //   final slotHeight = provider.timeSlotHeight;
  //
  //   // Calculate vertical offset: (hour - openTime) * slotHeight + (minute/60) * slotHeight
  //   final verticalOffset = ((targetHour - openTime) * slotHeight) + (targetMinute / 60 * slotHeight);
  //
  //   // Animate to the calculated vertical position
  //   _verticalScrollController!.animateTo(
  //     verticalOffset.clamp(0.0, _verticalScrollController!.position.maxScrollExtent),
  //     duration: const Duration(milliseconds: 500),
  //     curve: Curves.easeInOut,
  //   );
  //
  //   // Also scroll horizontally to the correct day if needed
  //   final weekDays = _getWeekDays(widget.selectedWeek);
  //   final dayIndex = weekDays.indexWhere((day) =>
  //     day.year == dateTime.year &&
  //     day.month == dateTime.month &&
  //     day.day == dateTime.day
  //   );
  //
  //   if (dayIndex != -1 && _horizontalScrollController != null && _horizontalScrollController!.hasClients) {
  //     final visibleStaff = provider.selectedStaff;
  //     const staffColumnWidth = 100.0;
  //     final horizontalOffset = dayIndex * visibleStaff.length * staffColumnWidth;
  //     final maxHorizontal = _horizontalScrollController!.position.maxScrollExtent;
  //
  //     _horizontalScrollController!.animateTo(
  //       horizontalOffset.clamp(0.0, maxHorizontal),
  //       duration: const Duration(milliseconds: 500),
  //       curve: Curves.easeInOut,
  //     );
  //   }
  // }

  /// Scroll to the current time
  void scrollToCurrentTime() {
    scrollToDateTime(DateTime.now());
  }

  /// Navigate to today and scroll to current time
  void goToToday() {
    // Just scroll to current time - week view handles date navigation differently
    Future.delayed(const Duration(milliseconds: 100), () {
      scrollToCurrentTime();
    });
  }

  Widget _buildCurrentTimeIndicator(int openTime, double slotHeight) {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    // In full day mode, show indicator for any hour; in business mode, only during business hours
    final businessCloseTime = widget.businessHours['closeTime'] as int;
    final showFullDay = context.read<CalendarProvider>().showFullDay;

    if (!showFullDay && (currentHour < openTime || currentHour >= businessCloseTime)) {
      return SizedBox.shrink();
    }

    if (showFullDay && (currentHour < openTime || currentHour >= 24)) {
      return SizedBox.shrink();
    }

    final topOffset = ((currentHour - openTime) * slotHeight) +
        (currentMinute / 60 * slotHeight);

    return Positioned(
      top: topOffset,
      left: 0,
      right: 0,
      child: Container(
        height: 2,
        color: Colors.red,
        child: Row(
          children: [
            Container(
              width: 4,
              height: 4,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            Expanded(
              child: Container(
                height: 2,
                color: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Scroll horizontally and vertically to show the specified date and time
  void scrollToDateTime(DateTime dateTime) {
    if (_horizontalScrollController != null &&
        _horizontalScrollController!.hasClients) {
      final index = _currentWeekDays.indexWhere((d) =>
          d.year == dateTime.year &&
          d.month == dateTime.month &&
          d.day == dateTime.day);
      if (index != -1) {
        final offset = index * _visibleStaffCount * _staffColumnWidth;
        final max = _horizontalScrollController!.position.maxScrollExtent;
        _horizontalScrollController!.animateTo(
          offset.clamp(0.0, max),
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }

    if (_verticalScrollController != null &&
        _verticalScrollController!.hasClients &&
        _slotHeight != null &&
        _openTime != null) {
      final offset = ((dateTime.hour - _openTime!) * _slotHeight!) +
          (dateTime.minute / 60 * _slotHeight!);
      final max = _verticalScrollController!.position.maxScrollExtent;
      _verticalScrollController!.animateTo(
        offset.clamp(0.0, max),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );

      if (_timeVerticalScrollController != null &&
          _timeVerticalScrollController!.hasClients) {
        final max2 = _timeVerticalScrollController!.position.maxScrollExtent;
        _timeVerticalScrollController!.animateTo(
          offset.clamp(0.0, max2),
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  // Feature 2: Helper methods for slot management (same as day view)
  String _getStaffDisplayName(StaffResponse staffMember) {
    // Check if this is a slot (additional staff member linked to original groomer)
    if (staffMember.notes?.contains('SLOT_FOR:') == true) {
      final originalStaffId = staffMember.notes!.split('SLOT_FOR:')[1].split('|')[0];
      final slotNumber = _getSlotNumber(staffMember.notes!);
      final originalStaff = context.read<CalendarProvider>().getStaffById(originalStaffId);
      return '${originalStaff?.displayName ?? 'Staff'} - Slot $slotNumber';
    }
    return staffMember.displayName;
  }

  int _getSlotNumber(String notes) {
    final match = RegExp(r'SLOT_NUMBER:(\d+)').firstMatch(notes);
    return match != null ? int.parse(match.group(1)!) : 1;
  }
  
}
