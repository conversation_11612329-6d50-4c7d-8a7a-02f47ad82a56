import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../config/theme/app_theme.dart';
import '../../models/salon_subscription.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/subscription_provider.dart';
import '../../services/auth/auth_service.dart';
import '../../services/calendar_preferences_service.dart';
import '../../services/staff_service.dart';
import '../../services/subscription_limit_service.dart';
import '../calendar_views/google_calendar_view.dart';
import '../forms/add_staff_dialog.dart';
import '../subscription/upgrade_prompt_card.dart';

class CalendarSettingsDrawer extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final ValueChanged<CalendarViewMode> onViewModeChanged;
  final bool monthlyViewEnabled;

  const CalendarSettingsDrawer({
    Key? key,
    required this.currentViewMode,
    required this.onViewModeChanged,
    required this.monthlyViewEnabled,
  }) : super(key: key);

  @override
  State<CalendarSettingsDrawer> createState() => _CalendarSettingsDrawerState();
}

class _CalendarSettingsDrawerState extends State<CalendarSettingsDrawer> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHourViewModeSection(),
                    const SizedBox(height: 24),
                    _buildStaffFilterSection(),
                    const SizedBox(height: 24),
                    _buildDisplayOptionsSection(),
                    const SizedBox(height: 24),
                    _buildZoomSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        'Setări Calendar',
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildHourViewModeSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Afișare ore',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            ToggleButtons(
              isSelected: [
                provider.hourViewMode == CalendarHourViewMode.businessHours,
                provider.hourViewMode == CalendarHourViewMode.fullDay,
              ],
              onPressed: (index) {
                final mode = index == 0
                    ? CalendarHourViewMode.businessHours
                    : CalendarHourViewMode.fullDay;
                provider.setHourViewMode(mode);
              },
              borderRadius: BorderRadius.circular(8),
              selectedColor: Theme.of(context).colorScheme.onPrimary,
              selectedBorderColor: Theme.of(context).colorScheme.primary,
              fillColor: Theme.of(context).colorScheme.primary,
              color: Theme.of(context).colorScheme.onSurface,
              constraints: const BoxConstraints(minWidth: 90, minHeight: 40),
              children: const [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.business, size: 16),
                      SizedBox(width: 6),
                      Text('Program', style: TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.schedule, size: 16),
                      SizedBox(width: 6),
                      Text('24 ore', style: TextStyle(fontSize: 13)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.hourViewMode == CalendarHourViewMode.businessHours
                  ? 'Afișează doar orele de program ale salonului'
                  : 'Afișează toate cele 24 de ore din zi',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffFilterSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final staff = provider.availableStaff;
        final selectedStaff = provider.selectedStaff;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtrare personal',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Consumer<CalendarProvider>(
              builder: (context, calendarProvider, child) {
                final allSelected = selectedStaff.length == staff.length;

                return Align(
                  alignment: Alignment.centerLeft,
                  child: TextButton(
                    onPressed: () {
                      if (allSelected) {
                        provider.clearStaffSelection();
                      } else {
                        provider.selectAllStaff();
                      }
                    },
                    child: Text(
                      allSelected ? 'Debifează tot' : 'Bifează tot',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                );
              },
            ),

            if (staff.isEmpty)
              Text(
                'Nu este personal disponibil',
                style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
              )
            else
              ...staff.map((staffMember) => _buildStaffOption(staffMember, provider)),
            const SizedBox(height: 8),

            const SizedBox(height: 12),
            _buildCreateStaffButton(),
          ],
        );
      },
    );
  }

  Widget _buildStaffOption(StaffResponse staffMember, CalendarProvider provider) {
    final isSelected = provider.selectedStaff.contains(staffMember.id);
    final staffColor = provider.getStaffColor(staffMember.id);

    return CheckboxListTile(
      value: isSelected,
      onChanged: (value) {
        if (value == true) {
          provider.selectStaff(staffMember.id);
        } else {
          provider.deselectStaff(staffMember.id);
        }
      },
      title: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: staffColor,
              shape: BoxShape.circle,
              border: Border.all(color: Theme.of(context).colorScheme.outline),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        staffMember.displayName,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ),
                  ],
                ),
                if (staffMember.nickname != null &&
                    staffMember.nickname!.isNotEmpty &&
                    staffMember.nickname != staffMember.name) ...[
                  Text(
                    '(${staffMember.name})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      activeColor: Theme.of(context).colorScheme.primary,
      contentPadding: EdgeInsets.zero,
    );
  }



  /// Handle add staff with intelligent upgrade check
  Future<void> _handleAddStaffWithUpgradeCheck(SubscriptionProvider subscriptionProvider) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) return;

      // Get current subscription and limits
      final currentTier = subscriptionProvider.currentTier;
      final limits = await SubscriptionLimitService.getLimits(salonId);
      final currentStaffCount = limits.currentStaffCount;
      final maxStaff = limits.maxStaff;

      // Check if user can add more staff
      if (maxStaff == -1 || currentStaffCount < maxStaff) {
        // User can add staff, show dialog directly
        _showAddStaffDialog();
        return;
      }

      // User has reached limit, show intelligent upgrade prompt
      if (mounted) {
        _showIntelligentUpgradePrompt(currentTier, salonId);
      }
    } catch (e) {
      debugPrint('❌ Error checking staff limits: $e');
      // Fallback to showing dialog
      _showAddStaffDialog();
    }
  }

  /// Show intelligent upgrade prompt based on current tier
  void _showIntelligentUpgradePrompt(SubscriptionTier? currentTier, String salonId) {
    String title;
    String message;
    SubscriptionTier targetTier;

    switch (currentTier) {
      case SubscriptionTier.free:
        title = 'Upgrade pentru Echipă';
        message = 'Planul gratuit permite doar 1 membru în echipă. Upgrade la Freelancer pentru până la 2 membri și funcții avansate.';
        targetTier = SubscriptionTier.freelancer;
        break;
      case SubscriptionTier.freelancer:
        title = 'Extinde-ți Echipa';
        message = 'Planul Freelancer permite doar 2 membri în echipă. Upgrade la Team pentru până la 5 membri și funcții avansate de management.';
        targetTier = SubscriptionTier.team;
        break;
      case SubscriptionTier.team:
        title = 'Echipă Nelimitată';
        message = 'Planul Team permite 5 membri în echipă. Upgrade la Enterprise pentru membri nelimitați și toate funcțiile premium.';
        targetTier = SubscriptionTier.enterprise;
        break;
      case SubscriptionTier.enterprise:
        // This shouldn't happen for Gold tier, but handle gracefully
        title = 'Limită Atinsă';
        message = 'Nu poți adăuga mai mulți membri în echipă cu planul curent.';
        targetTier = SubscriptionTier.enterprise;
        break;
      case null:
        // No subscription
        title = 'Limită Atinsă';
        message = 'Nu poți adăuga mai mulți membri în echipă cu planul curent.';
        targetTier = SubscriptionTier.team;
        break;
    }

    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          backgroundColor: Colors.transparent,
          child: UpgradePromptCard(
            title: title,
            message: message,
            requiredTier: targetTier,
            salonId: salonId,
            icon: Icons.group_add,
          ),
        ),
      );
    }
  }

  void _showAddStaffDialog() {
    showDialog(
      context: context,
      builder: (context) => AddStaffDialog(
        onSuccess: () async {
          debugPrint('🔄 CalendarSettingsDrawer: Staff added successfully, refreshing calendar data...');

          // Refresh staff data with multiple attempts to ensure new staff appears
          await _refreshStaffDataWithRetry();
        },
      ),
    );
  }

  /// Refresh staff data with retry mechanism to ensure new staff appears
  Future<void> _refreshStaffDataWithRetry() async {
    if (!mounted) return;

    final calendarProvider = context.read<CalendarProvider>();
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(milliseconds: 800);

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('🔄 CalendarSettingsDrawer: Refreshing staff data (attempt $attempt/$maxAttempts)');

      // Get staff count before refresh
      final staffCountBefore = calendarProvider.availableStaff.length;

      // Refresh staff data
      await calendarProvider.refreshStaffData();

      // Check if new staff appeared
      final staffCountAfter = calendarProvider.availableStaff.length;

      if (staffCountAfter > staffCountBefore) {
        debugPrint('✅ CalendarSettingsDrawer: New staff detected ($staffCountBefore -> $staffCountAfter)');

        // Auto-select the new staff member if it's the first one or if user preference
        await _autoSelectNewStaff(calendarProvider, staffCountBefore);
        break;
      }

      if (attempt < maxAttempts) {
        debugPrint('⏳ CalendarSettingsDrawer: No new staff detected, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      } else {
        debugPrint('⚠️ CalendarSettingsDrawer: Staff refresh completed but no new staff detected after $maxAttempts attempts');
      }
    }
  }

  /// Auto-select newly added staff member
  Future<void> _autoSelectNewStaff(CalendarProvider calendarProvider, int previousStaffCount) async {
    try {
      final currentStaff = calendarProvider.availableStaff;

      if (currentStaff.length > previousStaffCount) {
        // Get the newly added staff (assuming it's the last one in the list)
        final newStaff = currentStaff.last;

        debugPrint('🎯 CalendarSettingsDrawer: Auto-selecting new staff: ${newStaff.displayName} (${newStaff.id})');

        // Select the new staff member
        calendarProvider.selectStaff(newStaff.id);

        // Initialize working hours for the new staff member
        await _initializeNewStaffWorkingHours(newStaff.id, calendarProvider);

        // Force calendar refresh to ensure new staff appears in calendar view
        await calendarProvider.forceCalendarRefresh(reason: 'New staff added and selected');

        debugPrint('✅ CalendarSettingsDrawer: New staff auto-selected and initialized');
      }
    } catch (e) {
      debugPrint('❌ CalendarSettingsDrawer: Error auto-selecting new staff: $e');
    }
  }

  /// Initialize working hours for newly added staff
  Future<void> _initializeNewStaffWorkingHours(String staffId, CalendarProvider calendarProvider) async {
    try {
      debugPrint('🔧 CalendarSettingsDrawer: Initializing working hours for new staff: $staffId');

      // Force refresh staff working hours cache for the new staff member
      await calendarProvider.refreshStaffWorkingHours(reason: 'New staff added from calendar drawer');

      debugPrint('✅ CalendarSettingsDrawer: Working hours initialized for new staff');
    } catch (e) {
      debugPrint('❌ CalendarSettingsDrawer: Error initializing working hours for new staff: $e');
    }
  }



  Widget _buildCreateStaffButton() {
    return Center(
      child: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          return ElevatedButton.icon(
            onPressed: () => _handleAddStaffWithUpgradeCheck(subscriptionProvider),
            icon: const Icon(Icons.person_add),
            label: const Text('Adaugă membru nou'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        },
      ),
    );
  }



  Widget _buildDisplayOptionsSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Opțiuni afișare',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            CheckboxListTile(
              title: Text('Afișează programări anulate'),
              value: provider.showCanceledAppointments,
              onChanged: (value) {
                provider.setShowCanceledAppointments(value ?? false);
              },
              activeColor: Theme.of(context).colorScheme.primary,
              contentPadding: EdgeInsets.zero,
            ),
            const SizedBox(height: 16),
            Center(
              // child: TextButton(
              //   onPressed: () async {
              //     await provider.selectCurrentUserOnly();
              //
              //     // Ensure UI updates to reflect the selection
              //     setState(() {
              //     });
              //   },
              //   style: ElevatedButton.styleFrom(
              //     backgroundColor: Theme.of(context).colorScheme.primary,
              //     foregroundColor: Theme.of(context).colorScheme.onPrimary,
              //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              //     minimumSize: const Size(160, 40),
              //     shape: RoundedRectangleBorder(
              //       borderRadius: BorderRadius.circular(8),
              //     ),
              //   ),
              //   // child: const Row(
              //   //   mainAxisSize: MainAxisSize.min,
              //   //   // children: [
              //   //   //   Icon(Icons.person, size: 18),
              //   //   //   SizedBox(width: 8),
              //   //   //   Text('Doar eu', style: TextStyle(fontWeight: FontWeight.bold)),
              //   //   // ],
              //   // ),
              // ),
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildZoomSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Zoom și Accesibilitate',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),

            // Time slot height slider
            Row(
              children: [
                Icon(Icons.zoom_out, color: Theme.of(context).colorScheme.onSurface, size: 20),
                Expanded(
                  child: Slider(
                    value: provider.timeSlotHeight,
                    min: provider.minTimeSlotHeight,
                    max: provider.maxTimeSlotHeight,
                    divisions: 8,
                    activeColor: Theme.of(context).colorScheme.primary,
                    inactiveColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    onChanged: (value) {
                      provider.setTimeSlotHeight(value);
                    },
                  ),
                ),
                Icon(Icons.zoom_in, color: Theme.of(context).colorScheme.onSurface, size: 20),
              ],
            ),

            // Height indicator and quick actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Înălțime: ${provider.timeSlotHeight.round()}px',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: provider.zoomOut,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(40, 32),
                      ),
                      child: const Text('−', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ),
                    TextButton(
                      onPressed: provider.resetZoom,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(50, 32),
                      ),
                      child: const Text('Reset', style: TextStyle(fontSize: 12)),
                    ),
                    TextButton(
                      onPressed: provider.zoomIn,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(40, 32),
                      ),
                      child: const Text('+', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),
            Text(
              'Ajustează înălțimea intervalelor de timp pentru o vizibilitate mai bună',
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }
}
